/* Matrix QP Dashboard Styles */

/* Global Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1e1e1e;
    color: #ffffff;
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background-color: #1e1e1e;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #2c3e50, #e74c3c);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.header-subtitle {
    font-size: 1.2rem;
    color: #ecf0f1;
    margin-bottom: 0;
}

/* Control Panel */
.control-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #2c3e50;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-item {
    flex: 1;
    min-width: 200px;
}

.control-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #ecf0f1;
}

/* Form Controls */
.dropdown, .input {
    width: 100%;
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 5px;
    background-color: #34495e;
    color: #ffffff;
    font-size: 14px;
}

.dropdown:focus, .input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
}

/* Buttons */
.button-primary, .button-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.button-primary {
    background-color: #3498db;
    color: #ffffff;
}

.button-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.button-secondary {
    background-color: #95a5a6;
    color: #ffffff;
}

.button-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(149, 165, 166, 0.3);
}

.button-close-positions {
    padding: 12px 24px;
    border: none;
    border-radius: 8px; /* More rounded corners */
    font-size: 16px; /* Slightly larger font */
    font-weight: 700; /* Bolder text */
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #e74c3c; /* Bold red */
    color: #ffffff; /* White text */
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4); /* Stronger shadow */
    margin-top: 15px; /* Add some space above it */
    width: 100%; /* Make it full width in its container */
    letter-spacing: 0.5px; /* Slight letter spacing */
    text-transform: uppercase; /* Uppercase text */
}

.button-close-positions:hover {
    background-color: #c0392b; /* Darker red on hover */
    transform: translateY(-3px) scale(1.02); /* More pronounced hover effect */
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6); /* Even stronger shadow */
}

.button-close-positions:active {
    transform: translateY(0); /* Press effect */
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.3);
}

/* Help Text */
.help-text {
    font-size: 0.85rem;
    color: #95a5a6;
    font-style: italic;
    margin-top: 5px;
    line-height: 1.3;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 15px;
    background-color: #34495e;
    border-radius: 5px;
}

.status {
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 3px;
    background-color: #27ae60;
    color: #ffffff;
}

.timestamp {
    font-size: 0.9rem;
    color: #bdc3c7;
}

/* Main Content Layout */
.main-content {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.left-column, .right-column {
    flex: 1;
    min-width: 600px;
}

/* Portfolio Cards */
.portfolio-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.portfolio-card {
    background-color: #2c3e50;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    border-left: 4px solid #3498db;
}

.portfolio-card h4 {
    color: #3498db;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.metrics-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.metrics-summary > div {
    background-color: #34495e;
    padding: 10px;
    border-radius: 5px;
    min-width: 150px;
}

/* Data Tables */
.dash-table-container {
    background-color: #34495e !important;
    border-radius: 5px;
    overflow: hidden;
}

.dash-table-container .dash-spreadsheet-container {
    background-color: #34495e !important;
}

.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner {
    background-color: #34495e !important;
}

.dash-table-container table {
    background-color: #34495e !important;
    color: #ffffff !important;
}

.dash-table-container th {
    background-color: #2c3e50 !important;
    color: #3498db !important;
    font-weight: 600;
    border-bottom: 2px solid #3498db !important;
}

.dash-table-container td {
    background-color: #34495e !important;
    color: #ffffff !important;
    border-bottom: 1px solid #2c3e50 !important;
}

/* Charts */
.js-plotly-plot {
    background-color: #2c3e50 !important;
    border-radius: 10px;
    margin-bottom: 20px;
}

/* Placeholder */
.placeholder {
    text-align: center;
    padding: 40px;
    background-color: #34495e;
    border-radius: 10px;
    color: #bdc3c7;
    font-style: italic;
}

/* Section Headers */
h3 {
    color: #3498db;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-column, .right-column {
        min-width: 100%;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .header-title {
        font-size: 2rem;
    }
    
    .control-panel {
        flex-direction: column;
    }
    
    .control-item {
        min-width: 100%;
    }
    
    .metrics-summary {
        flex-direction: column;
    }
    
    .metrics-summary > div {
        min-width: 100%;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #34495e;
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error States */
.status-success {
    background-color: #27ae60 !important;
}

.status-error {
    background-color: #e74c3c !important;
}

.status-warning {
    background-color: #f39c12 !important;
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #2c3e50;
    color: #ffffff;
    text-align: center;
    border-radius: 5px;
    padding: 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2c3e50;
}

::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Print Styles */
@media print {
    .control-panel,
    .button-primary,
    .button-secondary {
        display: none !important;
    }
    
    .dashboard-container {
        background-color: white !important;
        color: black !important;
    }
    
    .portfolio-card {
        background-color: white !important;
        color: black !important;
        border: 1px solid #ccc !important;
    }
}

/* Auto-refresh toggle */
.toggle-checkbox {
    margin-top: 5px;
}

.toggle-checkbox .checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toggle-checkbox input[type="checkbox"] {
    transform: scale(1.3);
    accent-color: #3498db;
    cursor: pointer;
}

.toggle-checkbox label {
    font-size: 14px;
    color: #ecf0f1;
    cursor: pointer;
    user-select: none;
}

.control-label {
    color: #ecf0f1 !important;
    font-weight: 600 !important;
    margin-bottom: 5px !important;
}

/* Time Period Radio Buttons */
.time-period-container {
    background-color: #8B4513;
    border: 2px solid #A0522D;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.time-period-container label {
    color: #ffffff !important;
    font-weight: bold !important;
    font-size: 14px !important;
    margin-right: 15px !important;
}

/* Radio button styling */
input[type="radio"] {
    margin-right: 8px !important;
    margin-left: 10px !important;
    transform: scale(1.2);
    accent-color: #3498db;
}

/* Radio button labels */
.time-period-container .dash-radioitems label {
    color: #ecf0f1 !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    margin-right: 20px !important;
    cursor: pointer;
    transition: color 0.2s ease;
}

.time-period-container .dash-radioitems label:hover {
    color: #3498db !important;
}

/* Selected radio button styling */
.time-period-container .dash-radioitems input[type="radio"]:checked + label {
    color: #3498db !important;
    font-weight: 600 !important;
}

/* Radio items container */
.time-period-container .dash-radioitems {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

/* Responsive design for radio buttons */
@media (max-width: 768px) {
    .time-period-container .dash-radioitems {
        flex-direction: column;
        align-items: flex-start;
    }

    .time-period-container .dash-radioitems label {
        margin-bottom: 8px !important;
    }
}

/* Account Info Display */
.account-info {
    background-color: #34495e;
    border-radius: 5px;
    padding: 10px;
    font-size: 13px;
    color: #ecf0f1;
    border-left: 3px solid #3498db;
}

.account-info span {
    font-weight: 500;
}

/* Constraints for all right-side charts to prevent layout breaking */
#risk-metrics-chart,
#correlation-matrix-chart,
#portfolio-comparison-chart {
    max-height: 500px !important;
    height: 500px !important;
    overflow: hidden !important;
    position: relative !important;
}

/* Weight allocation chart needs flexible height for multiple portfolios */
#weight-allocation-chart {
    max-height: 500px !important;
    overflow: hidden !important;
    position: relative !important;
}

#risk-metrics-chart .js-plotly-plot,
#correlation-matrix-chart .js-plotly-plot,
#portfolio-comparison-chart .js-plotly-plot {
    max-height: 500px !important;
    height: 500px !important;
    overflow: hidden !important;
}

#weight-allocation-chart .js-plotly-plot {
    max-height: 500px !important;
    overflow: hidden !important;
}

#risk-metrics-chart .plotly-graph-div,
#correlation-matrix-chart .plotly-graph-div,
#portfolio-comparison-chart .plotly-graph-div {
    max-height: 500px !important;
    height: 500px !important;
    overflow: hidden !important;
}

#weight-allocation-chart .plotly-graph-div {
    max-height: 500px !important;
    overflow: hidden !important;
}
