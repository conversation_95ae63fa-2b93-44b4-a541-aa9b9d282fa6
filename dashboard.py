"""
Dashboard Module for Matrix QP
Creates interactive Dash/Plotly web interface for portfolio visualization
"""

import dash
from dash import dcc, html, Input, Output, State, callback_context, dash_table, ALL
from dash.exceptions import PreventUpdate
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List
from scipy import stats

from config import (
    DASH_HOST, DASH_PORT, DASH_DEBUG, CHART_THEME, CHART_HEIGHT, CHART_WIDTH,
    PORTFOLIO_UPDATE_INTERVAL, RISK_UPDATE_INTERVAL, DATA_UPDATE_INTERVAL,
    COLORS, MARKET_TIMEZONE
)
from dispersion_charts import Dispersion<PERSON><PERSON>Creator
from dispersion_calculator import DispersionCalculator

# Configure logging
logger = logging.getLogger(__name__)


class PortfolioDashboard:
    """
    Interactive dashboard for portfolio optimization visualization
    """
    
    def __init__(self):
        """Initialize the dashboard"""
        self.app = dash.Dash(__name__, suppress_callback_exceptions=True)
        self.app.title = "Matrix QP - Portfolio Optimization"

        # Initialize data stores
        self.current_portfolios = []
        self.current_risk_profiles = {}
        self.current_returns = pd.DataFrame()
        self.selected_portfolio = None
        self.selected_portfolio_data = None
        self.initial_load_complete = False  # Track if initial load is complete

        # Initialize optimization components
        from mt5_connector import MT5Connector
        from mt5_trader import MT5Trader
        from log_returns import LogReturnsCalculator
        from portfolio_optimizer import PortfolioOptimizer
        from weight_manager import WeightManager

        self.mt5_connector = MT5Connector()
        self.mt5_trader = MT5Trader(self.mt5_connector)
        self.returns_calculator = LogReturnsCalculator()
        self.portfolio_optimizer = PortfolioOptimizer()
        self.weight_manager = WeightManager()
        self.dispersion_calculator = DispersionCalculator()
        self.chart_creator = DispersionChartCreator()

        self._setup_layout()
        self._setup_callbacks()
    
    def _setup_layout(self):
        """Setup the dashboard layout"""
        self.app.layout = html.Div([
            # Header
            html.Div([
                html.H1("Matrix QP - Quantitative Portfolio Optimization", 
                       className="header-title"),
                html.P("Real-time forex portfolio optimization with advanced risk management",
                      className="header-subtitle"),
                html.Hr()
            ], className="header"),
            
            # Control Panel
            html.Div([
                html.Div([
                    html.Label("Optimization Strategy:"),
                    dcc.Dropdown(
                        id='strategy-dropdown',
                        options=[
                            {'label': 'Maximum Sharpe Ratio', 'value': 'max_sharpe'},
                            {'label': 'Maximum Sortino Ratio', 'value': 'max_sortino'},
                            {'label': 'Maximum Omega Ratio', 'value': 'max_omega'},
                            {'label': 'Maximum Calmar Ratio', 'value': 'max_calmar'},
                            {'label': 'Minimum Variance', 'value': 'min_variance'},
                            {'label': 'All Strategies', 'value': 'all'}
                        ],
                        value='all',
                        className="dropdown"
                    )
                ], className="control-item"),
                
                html.Div([
                    html.Label("Total Lot Size:"),
                    dcc.Input(
                        id='lot-size-input',
                        type='number',
                        value=0.5,
                        min=0.01,
                        step=0.01,
                        className="input"
                    )
                ], className="control-item"),
                
                html.Div([
                    html.Button("Optimize Portfolios",
                               id="optimize-button",
                               className="button-primary"),
                    html.Button("Refresh Data",
                               id="refresh-button",
                               className="button-secondary"),
                   ], className="control-item"),
    
                    html.Div([
                        html.Label("Auto-refresh:", className="control-label"),
                        dcc.Checklist(
                            id="auto-refresh-toggle",
                            options=[{"label": "Enable (5 min)", "value": "enabled"}],
                            value=[],  # Default disabled to prevent auto-sending
                            className="toggle-checkbox"
                        )
                    ], className="control-item"),
    
                    html.Div([
                        html.Label("Portfolio Diversification:", className="control-label"),
                        dcc.Checklist(
                            id="diversification-toggle",
                            options=[{"label": "Diversify across strategies", "value": "enabled"}],
                            value=["enabled"],  # Default enabled
                            className="toggle-checkbox"
                        ),
                        html.Small("Prevents same pairs from dominating multiple strategies",
                                   className="help-text")
                    ], className="control-item"),
    
                   # Account Information Display
                   html.Div([
                       html.Label("Account Info:", className="control-label"),
                       html.Div(id="account-info-display", className="account-info"),
                       html.Button("Close All MT5 Positions",
                                   id="close-all-positions-button",
                                   className="button-close-positions", # New class for styling
                                   n_clicks=0) # Initialize n_clicks to 0
                   ], className="control-item"),
                   dcc.ConfirmDialog(
                       id='confirm-close-all',
                       message='Are you sure you want to close all open MT5 positions?',
                   ),
               ], className="control-panel"),
           
           # Status Indicators
           html.Div([
               html.Div(id="status-indicator", className="status"),
               html.Div(id="last-update", className="timestamp")
           ], className="status-bar"),

            # Time Period Selection (moved to arrow location)
            html.Div([
                html.Label("Time Period:"),
                dcc.RadioItems(
                    id='time-period-selection',
                    options=[
                        {'label': '1h', 'value': '1h'},
                        {'label': '2h', 'value': '2h'},
                        {'label': '3h', 'value': '3h'},
                        {'label': '4h', 'value': '4h'},
                        {'label': '5h', 'value': '5h'},
                        {'label': '6h', 'value': '6h'},
                        {'label': '7h', 'value': '7h'},
                        {'label': '8h', 'value': '8h'},
                        {'label': '9h', 'value': '9h'},
                        {'label': '10h', 'value': '10h'},
                        {'label': '11h', 'value': '11h'},
                        {'label': '12h', 'value': '12h'},
                        {'label': '24h', 'value': '24h'},
                        {'label': '48h', 'value': '48h'},
                        {'label': '72h', 'value': '72h'},
                        {'label': '120h', 'value': '120h'}
                    ],
                    value='24h',  # Default to 24h
                    inline=True
                )
            ], className="time-period-container", style={'margin-bottom': '30px'}),

            # Alert Indicator and Configuration
            html.Div([
                html.Div(id="alert-indicator", style={'margin-bottom': '10px'}),
                html.Details([
                    html.Summary("Alert Settings", style={'cursor': 'pointer', 'color': '#4a90e2'}),
                    html.Div([
                        html.Label("Min Pairs for Alert:", style={'color': 'white', 'margin-right': '10px'}),
                        dcc.Input(
                            id='alert-min-pairs',
                            type='number',
                            value=5,
                            min=3,
                            max=15,
                            style={'width': '60px', 'margin-right': '20px'}
                        ),
                        html.Label("Acceleration Threshold:", style={'color': 'white', 'margin-right': '10px'}),
                        dcc.Input(
                            id='alert-acceleration',
                            type='number',
                            value=2.0,
                            min=0.5,
                            max=5.0,
                            step=0.1,
                            style={'width': '60px', 'margin-right': '20px'}
                        ),
                        html.Button("Update Settings", id="update-alert-settings",
                                  style={'backgroundColor': '#4a90e2', 'color': 'white', 'border': 'none', 'padding': '5px 10px'})
                    ], style={'padding': '10px', 'backgroundColor': '#2d3748', 'borderRadius': '5px'})
                ], style={'margin-bottom': '10px'})
            ], style={'margin-bottom': '20px'}),

            # Main Content
            html.Div([
                # Left Column - Portfolio Results
                html.Div([
                    html.H3("Optimized Portfolios"),
                    html.Div(id="portfolio-cards")
                ], className="left-column"),

                # Right Column - Risk Analysis
                html.Div([

                    html.H3("Risk Analysis"),
                    dcc.Graph(id="risk-metrics-chart",
                             config={'displayModeBar': True}),

                    html.H3("Weight Allocation"),
                    dcc.Graph(id="weight-allocation-chart",
                             config={'displayModeBar': True}),

                    # Currency Color Legend
                    html.Div([
                        html.H4("Currency Color Guide", style={'margin-bottom': '10px'}),
                        html.Div([
                            html.Span("EUR", style={'background-color': '#1f77b4', 'color': 'white', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("GBP", style={'background-color': '#d62728', 'color': 'white', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("AUD", style={'background-color': '#ff7f0e', 'color': 'white', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("NZD", style={'background-color': '#00ffff', 'color': 'black', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("USD", style={'background-color': '#2ca02c', 'color': 'white', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("CAD", style={'background-color': '#ff69b4', 'color': 'white', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("JPY", style={'background-color': '#ffff00', 'color': 'black', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                            html.Span("CHF", style={'background-color': '#808080', 'color': 'white', 'padding': '2px 8px', 'margin': '2px', 'border-radius': '3px', 'display': 'inline-block'}),
                        ], style={'display': 'flex', 'flex-wrap': 'wrap'}),
                        html.P("Colors show which currency is stronger: positive returns = base currency color, negative returns = quote currency color",
                               style={'font-size': '12px', 'margin-top': '5px', 'color': '#888'})
                    ], style={'margin-top': '20px', 'padding': '10px', 'border': '1px solid #444', 'border-radius': '5px'}),
                    
                    html.H3("Correlation Matrix"),
                    dcc.Graph(id="correlation-matrix-chart",
                             config={'displayModeBar': True}),

                    html.H3("Portfolio Comparison"),
                    dcc.Graph(id="portfolio-comparison-chart",
                             config={'displayModeBar': True})
                ], className="right-column")
            ], className="main-content"),

            # Portfolio Returns Chart (appears when portfolio is clicked)
            html.Div([
                html.H3("Combined Absolute Cumulative Returns (Current Day)", id="portfolio-returns-title"),
                dcc.Graph(id="portfolio-returns-chart",
                         config={'displayModeBar': True})
            ], id="portfolio-returns-section", style={'display': 'none'}),

            # Dispersion Analysis Charts
            html.Div([
                html.Hr(style={'margin': '30px 0', 'border-color': '#444'}),
                html.H2("Dispersion Analysis", style={'text-align': 'center', 'margin-bottom': '20px'}),

                # Rolling Dispersion Chart
                html.Div([
                    html.Div([
                        html.H3("Rolling Dispersion of Normalized Returns CSSD", style={'display': 'inline-block', 'margin-right': '20px'}),
                        html.Div([
                            dcc.Checklist(
                                id='adx-indicator-toggle',
                                options=[{'label': ' Show ADX Indicator', 'value': 'show_adx'}],
                                value=[],
                                style={'color': 'white', 'display': 'inline-block', 'margin-right': '15px'}
                            ),
                            html.Div([
                                html.Label("ADX Window:", style={'color': 'white', 'margin-right': '5px', 'font-size': '12px'}),
                                dcc.Input(
                                    id='adx-window-input',
                                    type='number',
                                    value=14,
                                    min=5,
                                    max=50,
                                    step=1,
                                    style={'width': '50px', 'height': '25px', 'font-size': '12px'}
                                )
                            ], style={'display': 'inline-block'})
                        ], style={'display': 'inline-block', 'vertical-align': 'top', 'margin-top': '5px'})
                    ], style={'display': 'flex', 'align-items': 'center', 'margin-bottom': '10px'}),
                    dcc.Graph(id="rolling-dispersion-chart",
                             config={'displayModeBar': True}),
                    # Near-peak pairs text area
                    html.Div([
                        html.Label("Pairs Near Peak Rolling Dispersion:",
                                  style={'font-weight': 'bold', 'margin-bottom': '5px'}),
                        dcc.Textarea(
                            id='rolling-dispersion-near-peak-edit',
                            value='',
                            style={
                                'width': '100%',
                                'height': '30px',
                                'backgroundColor': 'black',
                                'color': 'white',
                                'fontSize': '14px',
                                'marginBottom': '10px',
                                'border': '1px solid #444',
                                'borderRadius': '3px',
                                'padding': '5px'
                            },
                            readOnly=True,
                            spellCheck=False,
                            title='Pairs Near Peak Rolling Dispersion (read-only)'
                        )
                    ], style={'margin-top': '10px'})
                ], style={'margin-bottom': '30px'}),

                # Dispersion with Retracement Chart
                html.Div([
                    html.H3("Dispersion with Retracement"),
                    dcc.Graph(id="dispersion-retracement-chart",
                             config={'displayModeBar': True})
                ], style={'margin-bottom': '30px'}),

                # Portfolio Entry Warning System Chart
                html.Div([
                    html.Div([
                        html.H3("Portfolio Entry Warning System", style={'display': 'inline-block', 'margin-right': '20px'}),
                        html.Div([
                            html.Div([
                                html.Label("Lookback Window (min):", style={'color': 'white', 'margin-right': '5px', 'font-size': '12px'}),
                                dcc.Input(
                                    id='warning-lookback-window',
                                    type='number',
                                    value=240,
                                    min=60,
                                    max=480,
                                    step=30,
                                    style={'width': '60px', 'height': '25px', 'font-size': '12px', 'margin-right': '10px'}
                                )
                            ], style={'display': 'inline-block', 'margin-right': '15px'}),
                            html.Div([
                                html.Label("Threshold %ile:", style={'color': 'white', 'margin-right': '5px', 'font-size': '12px'}),
                                dcc.Input(
                                    id='warning-threshold-percentile',
                                    type='number',
                                    value=95.0,
                                    min=90.0,
                                    max=99.0,
                                    step=1.0,
                                    style={'width': '50px', 'height': '25px', 'font-size': '12px', 'margin-right': '10px'}
                                )
                            ], style={'display': 'inline-block', 'margin-right': '15px'}),
                            html.Div([
                                html.Label("ROC Sigma:", style={'color': 'white', 'margin-right': '5px', 'font-size': '12px'}),
                                dcc.Input(
                                    id='warning-roc-sigma',
                                    type='number',
                                    value=2.0,
                                    min=1.0,
                                    max=3.0,
                                    step=0.5,
                                    style={'width': '50px', 'height': '25px', 'font-size': '12px', 'margin-right': '10px'}
                                )
                            ], style={'display': 'inline-block', 'margin-right': '15px'}),
                            html.Div([
                                html.Label("ADX Threshold:", style={'color': 'white', 'margin-right': '5px', 'font-size': '12px'}),
                                dcc.Input(
                                    id='warning-adx-threshold',
                                    type='number',
                                    value=25.0,
                                    min=15.0,
                                    max=40.0,
                                    step=5.0,
                                    style={'width': '50px', 'height': '25px', 'font-size': '12px', 'margin-right': '10px'}
                                )
                            ], style={'display': 'inline-block', 'margin-right': '15px'}),
                            html.Button(
                                "Update Warning System",
                                id="update-warning-system-btn",
                                style={
                                    'backgroundColor': '#4a90e2',
                                    'color': 'white',
                                    'border': 'none',
                                    'padding': '5px 10px',
                                    'borderRadius': '3px',
                                    'fontSize': '12px'
                                },
                                n_clicks=0
                            )
                        ], style={'display': 'inline-block', 'vertical-align': 'top', 'margin-top': '5px'})
                    ], style={'display': 'flex', 'align-items': 'center', 'margin-bottom': '10px'}),
                    dcc.Graph(
                        id="portfolio-warning-system-chart",
                        figure=self._create_initial_warning_chart(),
                        config={'displayModeBar': True}
                    ),
                    # Warning system status
                    html.Div(
                        id="warning-system-status",
                        children=self._create_warning_status("🔄 Ready to load data - Click 'Update Warning System' or wait for automatic update"),
                        style={'margin-top': '10px'}
                    )
                ]),

                # Currency Rolling CSSD Dispersion Chart
                html.Div([
                    html.Hr(style={'margin': '30px 0', 'border-color': '#444'}),
                    html.H3("Rolling Dispersion of CSSD by Currency"),
                    html.P("Rolling dispersion (solid), IQR (dotted), and Cross-Sectional Mean μₜ (dashed) of CSSD for USD (green), EUR (blue), GBP (red), AUD (orange), NZD (aqua), CAD (pink), CHF (gray), JPY (yellow)",
                           style={'color': '#888', 'font-size': '14px', 'margin-bottom': '15px'}),
                    dcc.Graph(id="currency-cssd-chart",
                             config={'displayModeBar': True}),

                    # Pair Contribution Table
                    html.Div([
                        html.H4("Pair Contributions to CSSD (Last 30 Minutes)",
                               style={'margin-top': '30px', 'margin-bottom': '15px'}),
                        html.Div(id="pair-contributions-table")
                    ])
                ], style={'margin-bottom': '30px'})
            ], className="dispersion-section", style={'margin-top': '30px'}),

            # Data Stores
            dcc.Store(id='portfolios-store'),
            dcc.Store(id='risk-profiles-store'),
            dcc.Store(id='returns-store'),
            dcc.Store(id='optimization-status-store'),
            dcc.Store(id='time-period-store', data='24h'),
            
            # Intervals for auto-refresh
            dcc.Interval(
                id='portfolio-interval',
                interval=PORTFOLIO_UPDATE_INTERVAL,
                n_intervals=0
            ),
            dcc.Interval(
                id='risk-interval',
                interval=RISK_UPDATE_INTERVAL,
                n_intervals=0
            ),
            dcc.Interval(
                id='data-interval',
                interval=DATA_UPDATE_INTERVAL,
                n_intervals=0
            ),
            dcc.Interval(
                id='dispersion-interval',
                interval=60000,  # 1 minute (60,000 milliseconds)
                n_intervals=0
            ),
            dcc.Interval(
                id='account-interval',
                interval=1000,  # 1 seconds for frequent profit updates
                n_intervals=0
            )
        ], className="dashboard-container")
    
    def _reset_portfolio_selection(self):
        """Reset portfolio selection state"""
        self.selected_portfolio_index = None
        self.selected_portfolio_strategy = None
        self.selected_portfolio = None

    def _setup_callbacks(self):
        """Setup dashboard callbacks"""

        # Callback to update account information with frequent updates
        @self.app.callback(
            Output('account-info-display', 'children'),
            [Input('account-interval', 'n_intervals')]
        )
        def update_account_info(n_intervals):
            """Update account information display every 10 seconds"""
            try:
                account_info = self.mt5_connector.get_account_info()
                if account_info:
                    return html.Div([
                        html.Span(f"Server: {account_info['server']}",
                                style={'display': 'block', 'margin-bottom': '5px'}),
                        html.Span(f"Balance: {account_info['balance']:.2f} {account_info['currency']}",
                                style={'display': 'block', 'margin-bottom': '5px'}),
                        html.Span(f"Profit: {account_info['profit']:.2f} {account_info['currency']}",
                                style={'display': 'block'})
                    ])
                else:
                    return html.Span("Account info unavailable", style={'color': '#e74c3c'})
            except Exception as e:
                return html.Span(f"Error: {str(e)}", style={'color': '#e74c3c'})

        # Callback to handle time period selection
        @self.app.callback(
            Output('time-period-store', 'data'),
            [Input('time-period-selection', 'value')]
        )
        def update_time_period(selected_period):
            """Update time period store when selection changes"""
            return selected_period or '24h'

        @self.app.callback(
            Output('alert-indicator', 'children', allow_duplicate=True),
            [Input('update-alert-settings', 'n_clicks')],
            [State('alert-min-pairs', 'value'),
             State('alert-acceleration', 'value')],
            prevent_initial_call=True
        )
        def update_alert_settings(n_clicks, min_pairs, acceleration):
            """Update alert system settings"""
            if n_clicks:
                try:
                    from alert_system import takeoff_detector
                    takeoff_detector.min_pairs_threshold = min_pairs or 5
                    takeoff_detector.acceleration_threshold = acceleration or 2.0
                    return html.Div("✓ Alert settings updated",
                                  style={'color': '#4caf50', 'fontWeight': 'bold', 'padding': '5px'})
                except Exception as e:
                    return html.Div(f"✗ Error updating settings: {str(e)}",
                                  style={'color': '#f44336', 'fontWeight': 'bold', 'padding': '5px'})
            return ""

        @self.app.callback(
            [Output('portfolios-store', 'data'),
             Output('optimization-status-store', 'data'),
             Output('status-indicator', 'children')],
            [Input('optimize-button', 'n_clicks'),
             Input('refresh-button', 'n_clicks'),
             Input('portfolio-interval', 'n_intervals'),
             Input('time-period-store', 'data')],
            [State('strategy-dropdown', 'value'),
             State('lot-size-input', 'value'),
             State('auto-refresh-toggle', 'value'),
             State('diversification-toggle', 'value')]
        )
        def update_portfolios(n_clicks_optimize, n_clicks_refresh, n_intervals, time_period_data, strategy, lot_size, auto_refresh_enabled, diversification_enabled):
            """Update portfolio optimization results"""
            ctx = callback_context

            if not ctx.triggered:
                return [], {'status': 'idle'}, "Ready"

            trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

            # Prevent auto-optimization on startup to avoid auto-sending to MT5
            if trigger_id == 'portfolio-interval' and n_intervals == 1:
                return [], {'status': 'idle'}, "Ready - Click 'Optimize Portfolios' to start"

            # Check if auto-refresh is enabled
            auto_refresh_on = auto_refresh_enabled and 'enabled' in auto_refresh_enabled

            if trigger_id == 'optimize-button':
                try:
                    # Clear any cached data to ensure fresh calculations
                    logger.info("Clearing cached data for fresh optimization")
                    self.current_returns = None
                    self.extended_data = None
                    self.current_portfolios = []

                    # Force MT5 reconnection to ensure fresh data
                    if hasattr(self, 'mt5_connector') and self.mt5_connector.is_connected():
                        logger.info("Forcing MT5 reconnection for fresh data")
                        self.mt5_connector.disconnect()
                        if not self.mt5_connector.connect():
                            raise Exception("Failed to reconnect to MT5 for fresh data")

                    # Check if diversification is enabled
                    enable_diversification = diversification_enabled and 'enabled' in diversification_enabled

                    # Get time period for optimization
                    time_period = time_period_data if time_period_data else '24h'

                    # Full optimization - find new portfolios with fresh data
                    portfolios_data = self._run_real_optimization(strategy, enable_diversification, time_period)

                    status = {
                        'status': 'complete',
                        'timestamp': datetime.now().isoformat(),
                        'num_portfolios': len(portfolios_data)
                    }

                    status_text = f"✓ Optimized {len(portfolios_data)} portfolios"

                    return portfolios_data, status, status_text

                except Exception as e:
                    logger.error(f"Portfolio optimization failed: {str(e)}")
                    return [], {'status': 'error', 'error': str(e)}, f"✗ Error: {str(e)}"

            elif trigger_id == 'time-period-store':
                try:
                    # Skip initial automatic optimization on dashboard startup
                    if not self.initial_load_complete:
                        self.initial_load_complete = True
                        logger.info("Skipping initial automatic optimization - user should click 'Optimize Portfolios'")
                        return [], {'status': 'idle'}, "Ready - Click 'Optimize Portfolios' to start"

                    # Time period changed - run full optimization with new time period
                    logger.info(f"Time period changed to {time_period_data} - running full optimization")

                    # Clear any cached data to ensure fresh calculations
                    self.current_returns = None
                    self.extended_data = None

                    # Force MT5 reconnection to ensure fresh data
                    if hasattr(self, 'mt5_connector'):
                        self.mt5_connector.disconnect()
                        if not self.mt5_connector.connect():
                            raise Exception("Failed to reconnect to MT5 for fresh data")

                    # Check if diversification is enabled
                    enable_diversification = diversification_enabled and 'enabled' in diversification_enabled

                    # Get time period for optimization
                    time_period = time_period_data if time_period_data else '24h'

                    # Full optimization - find new portfolios for the new time period
                    portfolios_data = self._run_real_optimization(strategy, enable_diversification, time_period)

                    status = {
                        'status': 'complete',
                        'timestamp': datetime.now().isoformat(),
                        'num_portfolios': len(portfolios_data)
                    }

                    status_text = f"✓ Optimized {len(portfolios_data)} portfolios for {time_period}"

                    return portfolios_data, status, status_text

                except Exception as e:
                    logger.error(f"Time period optimization failed: {str(e)}")
                    return [], {'status': 'error', 'error': str(e)}, f"✗ Error: {str(e)}"

            elif trigger_id == 'portfolio-interval' and n_intervals > 0:
                try:
                    # Get current time period to maintain selection
                    time_period = time_period_data if time_period_data else '24h'

                    if auto_refresh_on:
                        # Auto-refresh enabled: run full optimization to find new portfolios
                        logger.info(f"Auto-refresh enabled: running full optimization with time period {time_period}")

                        # Check if diversification is enabled
                        enable_diversification = diversification_enabled and 'enabled' in diversification_enabled

                        portfolios_data = self._run_real_optimization(strategy, enable_diversification, time_period)
                        status_text = f"✓ Auto-optimized {len(portfolios_data)} portfolios for {time_period}"
                    else:
                        # Auto-refresh disabled: only refresh metrics of existing portfolios
                        logger.info("Auto-refresh disabled: refreshing existing portfolio metrics only")
                        portfolios_data = self._refresh_portfolio_metrics(time_period)
                        status_text = f"✓ Auto-refreshed data for {len(portfolios_data)} portfolios"

                    status = {
                        'status': 'complete',
                        'timestamp': datetime.now().isoformat(),
                        'num_portfolios': len(portfolios_data)
                    }

                    return portfolios_data, status, status_text

                except Exception as e:
                    logger.error(f"Auto-refresh failed: {str(e)}")
                    return [], {'status': 'error', 'error': str(e)}, f"✗ Auto-refresh error: {str(e)}"

            elif trigger_id == 'refresh-button':
                try:
                    # Set refresh flag to prevent automatic selections
                    self.refresh_in_progress = True

                    # Get current time period to maintain selection
                    time_period = time_period_data if time_period_data else '24h'

                    # Manual refresh - keep existing portfolios but update metrics
                    portfolios_data = self._refresh_portfolio_metrics(time_period)

                    status = {
                        'status': 'complete',
                        'timestamp': datetime.now().isoformat(),
                        'num_portfolios': len(portfolios_data)
                    }

                    status_text = f"✓ Refreshed data for {len(portfolios_data)} portfolios"

                    # Clear refresh flag after successful refresh (with small delay to prevent race conditions)
                    import threading
                    def clear_refresh_flag():
                        import time
                        time.sleep(0.5)  # Wait 500ms to ensure all callbacks complete
                        self.refresh_in_progress = False

                    threading.Thread(target=clear_refresh_flag, daemon=True).start()

                    return portfolios_data, status, status_text

                except Exception as e:
                    logger.error(f"Portfolio refresh failed: {str(e)}")
                    # Clear refresh flag on error
                    self.refresh_in_progress = False
                    return [], {'status': 'error', 'error': str(e)}, f"✗ Refresh error: {str(e)}"
            
            return [], {'status': 'idle'}, "Ready"
        
        @self.app.callback(
            Output('portfolio-cards', 'children'),
            [Input('portfolios-store', 'data'),
             Input('lot-size-input', 'value')]
        )
        def update_portfolio_cards(portfolios_data, lot_size):
            """Update portfolio display cards"""
            if not portfolios_data:
                return html.Div("No portfolios available. Click 'Optimize Portfolios' to start.",
                              className="placeholder")

            # Default lot size if None
            if lot_size is None:
                lot_size = 1.0

            cards = []
            for i, portfolio in enumerate(portfolios_data):
                card = self._create_portfolio_card(portfolio, i + 1, lot_size)
                cards.append(card)

            return html.Div(cards, className="portfolio-cards")
        
        @self.app.callback(
            Output('portfolio-comparison-chart', 'figure'),
            [Input('portfolios-store', 'data'),
             Input('time-period-store', 'data')]
        )
        def update_portfolio_comparison(portfolios_data, time_period_data):
            """Update portfolio comparison chart"""
            if not portfolios_data:
                return self._create_empty_figure("No portfolio data available")
            
            return self._create_portfolio_comparison_chart(portfolios_data)
        
        @self.app.callback(
            Output('risk-metrics-chart', 'figure'),
            [Input('portfolios-store', 'data'),
             Input('time-period-store', 'data')]
        )
        def update_risk_metrics(portfolios_data, time_period_data):
            """Update risk metrics chart"""
            if not portfolios_data:
                return self._create_empty_figure("No risk data available")
            
            return self._create_risk_metrics_chart(portfolios_data)
        
        @self.app.callback(
            Output('weight-allocation-chart', 'figure'),
            [Input('portfolios-store', 'data'),
             Input('time-period-store', 'data')]
        )
        def update_weight_allocation(portfolios_data, time_period_data):
            """Update weight allocation chart"""
            if not portfolios_data:
                return self._create_empty_figure("No weight data available")
            
            return self._create_weight_allocation_chart(portfolios_data)
        

        
        @self.app.callback(
            Output('last-update', 'children'),
            [Input('optimization-status-store', 'data')]
        )
        def update_timestamp(status_data):
            """Update last update timestamp"""
            if not status_data or 'timestamp' not in status_data:
                return ""

            timestamp = datetime.fromisoformat(status_data['timestamp'])
            return f"Last updated: {timestamp.strftime('%H:%M:%S')}"

        # Store for tracking portfolio selection
        self.selected_portfolio_index = None
        self.selected_portfolio_strategy = None  # Track strategy to preserve selection across refreshes
        self.refresh_in_progress = False  # Flag to prevent automatic selections during refresh

        @self.app.callback(
            Output('portfolios-store', 'data', allow_duplicate=True),
            [Input({'type': 'portfolio-btn', 'index': ALL}, 'n_clicks')],
            [State('portfolios-store', 'data')],
            prevent_initial_call=True
        )
        def handle_portfolio_selection(n_clicks_list, portfolios_data):
            """Handle portfolio button clicks"""
            ctx = callback_context

            if not ctx.triggered or not portfolios_data:
                raise PreventUpdate

            # Ignore automatic selections during refresh
            if self.refresh_in_progress:
                raise PreventUpdate

            # Find which button was clicked
            button_id = ctx.triggered[0]['prop_id']
            if 'portfolio-btn' in button_id:
                # Extract index from button ID
                import json
                button_info = json.loads(button_id.split('.')[0])
                self.selected_portfolio_index = button_info['index']

                # Also store the strategy to preserve selection across refreshes
                if portfolios_data and self.selected_portfolio_index < len(portfolios_data):
                    self.selected_portfolio_strategy = portfolios_data[self.selected_portfolio_index].get('strategy')

            return portfolios_data  # Return same data to trigger chart update

        @self.app.callback(
            [Output('portfolio-returns-section', 'style'),
             Output('portfolio-returns-chart', 'figure'),
             Output('portfolio-returns-title', 'children'),
             Output('correlation-matrix-chart', 'figure')],
            [Input('portfolios-store', 'data'),
             Input('time-period-store', 'data')]
        )
        def update_portfolio_charts(portfolios_data, time_period_data):
            """Update portfolio charts based on stored selection"""
            if not portfolios_data:
                return {'display': 'none'}, self._create_empty_figure("Click 'View Details' on a portfolio to see combined absolute cumulative returns"), "Combined Absolute Cumulative Returns (Current Day)", self._create_empty_figure("Click 'View Details' on a portfolio to see correlation matrix")

            # Try to preserve the selected portfolio across refreshes
            if self.selected_portfolio_strategy is not None:
                # Find the portfolio with the same strategy
                for i, portfolio in enumerate(portfolios_data):
                    if portfolio.get('strategy') == self.selected_portfolio_strategy:
                        self.selected_portfolio_index = i
                        break
                else:
                    # Strategy not found, reset selection
                    self.selected_portfolio_index = None
                    self.selected_portfolio_strategy = None

            # If no portfolio is selected, don't show charts
            if self.selected_portfolio_index is None:
                return {'display': 'none'}, self._create_empty_figure("Click 'View Details' on a portfolio to see combined absolute cumulative returns"), "Combined Absolute Cumulative Returns (Current Day)", self._create_empty_figure("Click 'View Details' on a portfolio to see correlation matrix")

            # Validate the selected index
            if self.selected_portfolio_index >= len(portfolios_data):
                # Index is invalid, reset selection
                self.selected_portfolio_index = None
                self.selected_portfolio_strategy = None
                return {'display': 'none'}, self._create_empty_figure("Selected portfolio not available"), "Combined Absolute Cumulative Returns (Current Day)", self._create_empty_figure("Selected portfolio not available")

            # Get selected portfolio
            selected_portfolio = portfolios_data[self.selected_portfolio_index]
            self.selected_portfolio = selected_portfolio

            # Create portfolio returns chart with time period
            time_period = time_period_data if time_period_data else '24h'
            returns_chart = self._create_portfolio_returns_chart(selected_portfolio, time_period)

            # Create correlation matrix for selected portfolio pairs
            correlation_chart = self._create_portfolio_correlation_matrix(selected_portfolio)

            # Update title with time period
            strategy = selected_portfolio.get('strategy', 'Unknown').replace('_', ' ').title()
            time_period = time_period_data if time_period_data else '24h'

            # Create descriptive time period text
            if time_period == '24h':
                period_text = "Current Day"
            elif time_period.endswith('h'):
                hours = int(time_period[:-1])
                if hours < 24:
                    period_text = f"Last {hours} Hours"
                else:
                    days = hours // 24
                    period_text = f"Last {days} Days"
            else:
                period_text = time_period.upper()

            title = f"Combined Absolute Cumulative Returns ({period_text}) - {strategy}"

            return {'display': 'block'}, returns_chart, title, correlation_chart

        @self.app.callback(
            Output('status-indicator', 'children', allow_duplicate=True),
            [Input({'type': 'mt5-trade-btn', 'index': ALL}, 'n_clicks')],
            [State('portfolios-store', 'data'),
             State('lot-size-input', 'value')],
            prevent_initial_call=True
        )
        def handle_mt5_trade(n_clicks_list, portfolios_data, lot_size):
            """Handle MT5 trade button clicks"""
            ctx = callback_context

            if not ctx.triggered or not portfolios_data:
                raise PreventUpdate

            # Check if any button was actually clicked (not just initialized)
            if not any(clicks for clicks in n_clicks_list if clicks is not None and clicks > 0):
                raise PreventUpdate

            # Find which button was clicked
            button_id = ctx.triggered[0]['prop_id']
            if 'mt5-trade-btn' in button_id:
                try:
                    # Extract index from button ID
                    import json
                    button_info = json.loads(button_id.split('.')[0])
                    portfolio_index = button_info['index']

                    if portfolio_index >= len(portfolios_data):
                        return "✗ Portfolio not found"

                    # Get portfolio data
                    portfolio = portfolios_data[portfolio_index]
                    strategy = portfolio.get('strategy', 'unknown')

                    # Use default lot size if None
                    if lot_size is None:
                        lot_size = 1.0

                    logger.info(f"Sending {strategy} portfolio to MT5 with lot size {lot_size}")

                    # Execute trades without closing existing positions
                    result = self.mt5_trader.send_portfolio_to_mt5(portfolio, lot_size, close_existing=False)

                    if result['success']:
                        successful_trades = sum(1 for trade in result['trades'] if trade['success'])
                        total_trades = len(result['trades'])
                        return f"✓ MT5: {successful_trades}/{total_trades} trades executed for {strategy}"
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        return f"✗ MT5 Error: {error_msg}"

                except Exception as e:
                    logger.error(f"MT5 trade execution failed: {str(e)}")
                    return f"✗ MT5 Error: {str(e)}"

            raise PreventUpdate

        @self.app.callback(
            Output('confirm-close-all', 'displayed'),
            [Input('close-all-positions-button', 'n_clicks')],
            prevent_initial_call=True
        )
        def trigger_close_all_confirmation(n_clicks):
            """Trigger confirmation dialog for closing all positions"""
            logger.info(f"Close all positions button clicked. n_clicks: {n_clicks}")
            if n_clicks and n_clicks > 0:
                logger.info("Displaying confirmation dialog for closing all positions")
                return True
            return False

        @self.app.callback(
            [Output('status-indicator', 'children', allow_duplicate=True),
             Output('confirm-close-all', 'displayed', allow_duplicate=True)],
            [Input('confirm-close-all', 'submit_n_clicks'),
             Input('confirm-close-all', 'cancel_n_clicks')],
            prevent_initial_call=True
        )
        def confirm_and_close_all_positions(submit_n_clicks, cancel_n_clicks):
            """Handle confirmation dialog and close all positions if confirmed"""
            ctx = callback_context
            if not ctx.triggered:
                logger.debug("No trigger context for close all positions callback")
                raise PreventUpdate

            trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
            logger.info(f"Close all positions confirmation triggered by: {trigger_id}")

            if trigger_id == 'confirm-close-all' and submit_n_clicks:
                logger.info("User confirmed closing all MT5 positions - proceeding with closure")
                try:
                    # Ensure MT5 connection before attempting to close positions
                    if not self.mt5_connector.ensure_connection():
                        error_msg = "Failed to connect to MT5 - cannot close positions"
                        logger.error(error_msg)
                        return (html.Div(f"✗ {error_msg}",
                                       style={'color': '#f44336', 'fontWeight': 'bold', 'padding': '5px'}),
                               False)
                    
                    # Call the close all positions method
                    result = self.mt5_connector.close_all_positions()
                    closed_count = result.get("closed", 0)
                    failed_count = result.get("failed", 0)
                    
                    logger.info(f"Close all positions result: {closed_count} closed, {failed_count} failed")
                    
                    if closed_count > 0:
                        success_msg = f"✓ Successfully closed {closed_count} positions"
                        if failed_count > 0:
                            success_msg += f" (Failed: {failed_count})"
                        return (html.Div(success_msg,
                                       style={'color': '#4caf50', 'fontWeight': 'bold', 'padding': '5px'}),
                               False)
                    elif failed_count > 0:
                        error_msg = f"✗ Failed to close {failed_count} positions"
                        return (html.Div(error_msg,
                                       style={'color': '#f44336', 'fontWeight': 'bold', 'padding': '5px'}),
                               False)
                    else:
                        info_msg = "ℹ No open positions found to close"
                        return (html.Div(info_msg,
                                       style={'color': '#2196f3', 'fontWeight': 'bold', 'padding': '5px'}),
                               False)
                        
                except Exception as e:
                    error_msg = f"Error closing positions: {str(e)}"
                    logger.error(f"Exception in close_all_positions: {error_msg}")
                    return (html.Div(f"✗ {error_msg}",
                                   style={'color': '#f44336', 'fontWeight': 'bold', 'padding': '5px'}),
                           False)
                           
            elif trigger_id == 'confirm-close-all' and cancel_n_clicks:
                logger.info("User cancelled closing all positions")
                return (html.Div("Closing positions cancelled",
                               style={'color': '#ff9800', 'fontWeight': 'bold', 'padding': '5px'}),
                       False)
            
            logger.debug("No valid trigger for close all positions callback")
            raise PreventUpdate

        # Callback for dispersion charts
        @self.app.callback(
            [Output('rolling-dispersion-chart', 'figure'),
             Output('dispersion-retracement-chart', 'figure'),
             Output('rolling-dispersion-near-peak-edit', 'value'),
             Output('alert-indicator', 'children', allow_duplicate=True)],
            [Input('dispersion-interval', 'n_intervals'),
             Input('adx-indicator-toggle', 'value'),
             Input('adx-window-input', 'value')],
            prevent_initial_call=True
        )
        def update_dispersion_charts(n_intervals, adx_toggle, adx_window):
            """Update dispersion charts every minute and check for takeoff alerts"""
            try:
                # Import dispersion modules
                from dispersion_calculator import DispersionCalculator
                from dispersion_charts import DispersionChartCreator
                from alert_system import check_for_takeoff, send_alert

                # Fetch current market data
                market_data = self.mt5_connector.fetch_daily_data(current_day_only=True)

                if not market_data:
                    logger.warning("No market data available for dispersion charts")
                    return self._create_empty_dispersion_charts()

                # Calculate log returns
                log_returns = self.returns_calculator.calculate_log_returns(market_data)

                if log_returns.empty:
                    logger.warning("No log returns available for dispersion charts")
                    return self._create_empty_dispersion_charts()

                # Initialize calculators
                dispersion_calc = DispersionCalculator()
                chart_creator = DispersionChartCreator()

                # Calculate cumulative returns since start of day
                cumulative_returns = dispersion_calc.calculate_cumulative_returns_since_sod(market_data)

                if cumulative_returns.empty:
                    logger.warning("No cumulative returns available for dispersion charts")
                    return self._create_empty_dispersion_charts()

                # Calculate realized volatility
                realized_volatility = dispersion_calc.calculate_realized_volatility(market_data)

                if realized_volatility.empty:
                    logger.warning("No realized volatility available for dispersion charts")
                    return self._create_empty_dispersion_charts()

                # Normalize returns by realized volatility
                normalized_returns = dispersion_calc.normalize_returns_by_realized_vol(
                    cumulative_returns, realized_volatility
                )

                if normalized_returns.empty:
                    logger.warning("No normalized returns available for dispersion charts")
                    return self._create_empty_dispersion_charts()

                # Calculate dispersion metrics using normalized returns for better analysis
                # 1. Dispersion with Retracement: Uses dispersion calculated from normalized returns
                dispersion_ts_normalized = dispersion_calc.calculate_dispersion_metric(normalized_returns)
                retracement_pct = dispersion_calc.calculate_retracement_percentage(dispersion_ts_normalized)

                # 1b. Rolling Dispersion of CSSD: Expanding std of CSSD values (volatility of dispersion)
                rolling_cssd_dispersion = dispersion_calc.calculate_rolling_cssd_dispersion(dispersion_ts_normalized)

                # 2. Rolling Dispersion: Uses normalized returns for equal-weighted pair analysis
                rolling_dispersion_df = dispersion_calc.calculate_rolling_dispersion(normalized_returns)

                # Determine ADX settings
                show_adx = 'show_adx' in (adx_toggle or [])
                adx_window_value = adx_window if adx_window and adx_window > 0 else 14

                # Create charts
                rolling_chart_result = chart_creator.create_rolling_dispersion_chart(
                    normalized_returns, rolling_dispersion_df, show_adx, adx_window_value
                )

                retracement_chart = chart_creator.create_dispersion_with_retracement_chart(
                    dispersion_ts_normalized, retracement_pct, rolling_cssd_dispersion
                )

                # Get near-peak weights string
                near_peak_weights = rolling_chart_result.get('near_peak_weights_str', '')

                # Check for takeoff alerts
                alert_indicator = ""
                try:
                    alert_event = check_for_takeoff(normalized_returns)
                    if alert_event:
                        send_alert(alert_event)
                        alert_indicator = html.Div([
                            html.Span("🚀 ", style={'fontSize': '20px'}),
                            html.Span(f"TAKEOFF ALERT: {len(alert_event.pairs_involved)} pairs taking off!",
                                    style={'color': '#ff6b6b', 'fontWeight': 'bold'}),
                            html.Br(),
                            html.Small(f"Pairs: {', '.join(alert_event.pairs_involved[:5])}" +
                                     ("..." if len(alert_event.pairs_involved) > 5 else ""),
                                     style={'color': '#ffa726'})
                        ], style={'padding': '10px', 'backgroundColor': '#2d3748', 'borderRadius': '5px', 'margin': '5px'})
                except Exception as e:
                    logger.error(f"Error in takeoff detection: {str(e)}")

                logger.info("Successfully updated dispersion charts")

                return (
                    rolling_chart_result['figure'],
                    retracement_chart,
                    near_peak_weights,
                    alert_indicator
                )

            except Exception as e:
                logger.error(f"Error updating dispersion charts: {str(e)}")
                return self._create_empty_dispersion_charts()

        # Callback for Portfolio Entry Warning System
        @self.app.callback(
            [Output('portfolio-warning-system-chart', 'figure'),
             Output('warning-system-status', 'children')],
            [Input('update-warning-system-btn', 'n_clicks'),
             Input('dispersion-interval', 'n_intervals')],
            [State('warning-lookback-window', 'value'),
             State('warning-threshold-percentile', 'value'),
             State('warning-roc-sigma', 'value'),
             State('warning-adx-threshold', 'value')],
            prevent_initial_call=False
        )
        def update_warning_system_chart(n_clicks, n_intervals, lookback_window,
                                       threshold_percentile, roc_sigma, adx_threshold):
            """Update the Portfolio Entry Warning System chart"""
            try:
                logger.info(f"🔥 WARNING SYSTEM CALLBACK TRIGGERED - n_clicks: {n_clicks}, n_intervals: {n_intervals}")
                logger.info(f"Parameters: lookback={lookback_window}, threshold={threshold_percentile}, roc_sigma={roc_sigma}, adx_threshold={adx_threshold}")

                # Fetch current market data (try current day first, then previous trading day)
                logger.info("Fetching market data for warning system...")
                market_data = self.mt5_connector.fetch_daily_data(current_day_only=True)

                data_source = "today"
                if not market_data:
                    logger.info("No current day data available, trying last 24 hours...")
                    # Try to get last 24 hours of data (useful for weekends)
                    market_data = self.mt5_connector.fetch_daily_data(time_period='24h')
                    data_source = "last 24h"

                    if not market_data:
                        logger.info("No 24h data available, trying last 48 hours...")
                        # Try to get last 48 hours of data (covers Friday if it's weekend)
                        market_data = self.mt5_connector.fetch_daily_data(time_period='48h')
                        data_source = "last 48h"

                        if not market_data:
                            logger.warning("No market data available for warning system")
                            return self._create_empty_warning_chart(), self._create_warning_status("❌ No market data available - check MT5 connection or market hours")

                # Calculate log returns
                logger.info("Calculating log returns...")
                log_returns = self.returns_calculator.calculate_log_returns(market_data)

                if log_returns.empty:
                    logger.warning("No log returns available for warning system")
                    return self._create_empty_warning_chart(), self._create_warning_status("❌ No log returns available - insufficient market data")

                logger.info(f"Log returns calculated: {log_returns.shape}")

                # Calculate cumulative returns since start of day
                logger.info("Calculating cumulative returns...")
                cumulative_returns = self.dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)

                if cumulative_returns.empty:
                    logger.warning("No cumulative returns available for warning system")
                    return self._create_empty_warning_chart(), self._create_warning_status("❌ No cumulative returns available - check data processing")

                # Calculate realized volatility and normalize returns
                realized_volatility = self.dispersion_calculator.calculate_realized_volatility(market_data)
                normalized_returns = self.dispersion_calculator.normalize_returns_by_realized_vol(
                    cumulative_returns, realized_volatility
                )

                if normalized_returns.empty:
                    logger.warning("No normalized returns available for warning system")
                    return self._create_empty_warning_chart(), self._create_warning_status("No normalized returns available")

                # Calculate CSSD dispersion
                cssd_series = self.dispersion_calculator.calculate_dispersion_metric(normalized_returns)

                if cssd_series.empty:
                    logger.warning("No CSSD data available for warning system")
                    return self._create_empty_warning_chart(), self._create_warning_status("No CSSD data available")

                # Set default values if None
                lookback_window = lookback_window or 240
                threshold_percentile = threshold_percentile or 95.0
                roc_sigma = roc_sigma or 2.0
                adx_threshold = adx_threshold or 25.0

                # Create warning system chart
                warning_chart = self.chart_creator.create_portfolio_entry_warning_chart(
                    cssd_series=cssd_series,
                    normalized_returns_ts=normalized_returns,
                    lookback_window=lookback_window,
                    threshold_percentile=threshold_percentile,
                    roc_window=15,
                    roc_sigma_multiplier=roc_sigma,
                    persistence_bars=3,
                    pre_quiet_window=60,
                    adx_window=14,
                    adx_threshold=adx_threshold,
                    entry_window_minutes=30
                )

                # Create status message
                latest_cssd = cssd_series.iloc[-1] if not cssd_series.empty else 0
                status_message = self._create_warning_status(
                    f"✅ Data source: {data_source} | Latest CSSD: {latest_cssd:.4f} | Data points: {len(cssd_series)} | "
                    f"Time range: {cssd_series.index[0].strftime('%H:%M')} - {cssd_series.index[-1].strftime('%H:%M')}"
                )

                logger.info("Successfully updated warning system chart")
                return warning_chart, status_message

            except Exception as e:
                logger.error(f"Error updating warning system chart: {str(e)}")
                return self._create_empty_warning_chart(), self._create_warning_status(f"Error: {str(e)}")

        # Callback for Currency CSSD Chart and Table
        @self.app.callback(
            [Output('currency-cssd-chart', 'figure'),
             Output('pair-contributions-table', 'children')],
            [Input('dispersion-interval', 'n_intervals')],
            prevent_initial_call=True
        )
        def update_currency_cssd_chart(n_intervals):
            """Update the Currency CSSD chart every minute"""
            try:
                logger.info(f"🔥 CURRENCY CSSD CALLBACK TRIGGERED - n_intervals: {n_intervals}")

                # Fetch current market data
                market_data = self.mt5_connector.fetch_daily_data(current_day_only=True)

                if not market_data:
                    logger.warning("No market data available for currency CSSD chart")
                    empty_chart = self._create_empty_currency_cssd_chart()
                    empty_table = html.Div("No data available", style={'text-align': 'center', 'color': '#888'})
                    return empty_chart, empty_table

                # Calculate log returns
                log_returns = self.returns_calculator.calculate_log_returns(market_data)

                if log_returns.empty:
                    logger.warning("No log returns available for currency CSSD chart")
                    empty_chart = self._create_empty_currency_cssd_chart()
                    empty_table = html.Div("No data available", style={'text-align': 'center', 'color': '#888'})
                    return empty_chart, empty_table

                # Calculate cumulative returns since start of day
                cumulative_returns = self.dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)

                if cumulative_returns.empty:
                    logger.warning("No cumulative returns available for currency CSSD chart")
                    empty_chart = self._create_empty_currency_cssd_chart()
                    empty_table = html.Div("No data available", style={'text-align': 'center', 'color': '#888'})
                    return empty_chart, empty_table

                # Calculate realized volatility and normalize returns
                realized_volatility = self.dispersion_calculator.calculate_realized_volatility(market_data)
                normalized_returns = self.dispersion_calculator.normalize_returns_by_realized_vol(
                    cumulative_returns, realized_volatility
                )

                if normalized_returns.empty:
                    logger.warning("No normalized returns available for currency CSSD chart")
                    empty_chart = self._create_empty_currency_cssd_chart()
                    empty_table = html.Div("No data available", style={'text-align': 'center', 'color': '#888'})
                    return empty_chart, empty_table

                # Create currency CSSD chart and get pair contributions with μₜ values
                currency_cssd_chart, pair_contributions, currency_weights, sharpe_optimized_allocation, currency_mu_t_latest = self.chart_creator.create_currency_cssd_chart(normalized_returns)

                # Create pair contributions table with currency weights and μₜ signs
                contributions_table = self._create_pair_contributions_table(pair_contributions, currency_weights, sharpe_optimized_allocation, currency_mu_t_latest)

                logger.info("Successfully updated currency CSSD chart and table")
                return currency_cssd_chart, contributions_table

            except Exception as e:
                logger.error(f"Error updating currency CSSD chart: {str(e)}")
                empty_chart = self._create_empty_currency_cssd_chart()
                empty_table = html.Div("Error loading data", style={'text-align': 'center', 'color': '#ff6b6b'})
                return empty_chart, empty_table

    def _create_empty_dispersion_charts(self):
        """Create empty dispersion charts when data is not available"""
        empty_rolling = go.Figure().update_layout(
            title="Rolling Dispersion of Normalized Returns CSSD",
            template="plotly_dark",
            height=800,
            width=900,
            autosize=False,
            annotations=[dict(text="No data available", x=0.5, y=0.5, showarrow=False)]
        )

        empty_retracement = go.Figure().update_layout(
            title="Dispersion with Retracement",
            template="plotly_dark",
            height=800,
            width=900,
            autosize=False,
            annotations=[dict(text="No data available", x=0.5, y=0.5, showarrow=False)]
        )

        return empty_rolling, empty_retracement, "", ""



    def _create_empty_warning_chart(self):
        """Create empty warning system chart when data is not available"""
        fig = go.Figure()
        fig.add_annotation(
            text="Click 'Update Warning System' to load data<br>or wait for automatic update",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=16, color="white"),
            bgcolor="rgba(68, 68, 68, 0.8)",
            bordercolor="white",
            borderwidth=1
        )
        fig.update_layout(
            title="Portfolio Entry Warning System - Dispersion Breakout Strategy",
            template="plotly_dark",
            height=900,
            width=1400,
            autosize=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)
        )
        return fig

    def _create_pair_contributions_table(self, pair_contributions: dict, currency_weights: dict = None, sharpe_optimized_allocation: str = None, currency_mu_t_latest: dict = None) -> html.Div:
        """
        Create HTML table showing pair contributions to CSSD for each currency.

        Args:
            pair_contributions: Dictionary with currency -> list of (pair, contribution) tuples
            currency_weights: Dictionary with currency -> list of (pair, normalized_weight) tuples
            sharpe_optimized_allocation: String with Sharpe-optimized allocation
            currency_mu_t_latest: Dictionary with currency -> latest μₜ value for sign calculation

        Returns:
            HTML Div containing the table
        """
        if not pair_contributions:
            return html.Div("No contribution data available",
                          style={'text-align': 'center', 'color': '#888'})

        # Currency colors from dispersion_charts.py
        CURRENCY_COLORS = {
            'USD': '#00ff00',  # Green
            'EUR': '#0080ff',  # Blue
            'GBP': '#ff0000',  # Red
            'AUD': '#ff8000',  # Orange
            'NZD': '#00ffff',  # Aqua
            'CAD': '#ff00ff',  # Magenta/Pink
            'CHF': '#808080',  # Gray
            'JPY': '#ffff00'   # Yellow
        }

        currencies = ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']

        # Calculate currency signs based on μₜ (cross-sectional mean) from Rolling Dispersion of CSSD
        currency_signs = {}
        if currency_mu_t_latest:
            for currency in currencies:
                if currency in currency_mu_t_latest:
                    latest_mu_t = currency_mu_t_latest[currency]
                    currency_signs[currency] = "+" if latest_mu_t >= 0 else "-"
                else:
                    currency_signs[currency] = ""
        else:
            # No μₜ data available, use empty signs
            for currency in currencies:
                currency_signs[currency] = ""

        # Create table headers
        header_cells = []
        for currency in currencies:
            color = CURRENCY_COLORS.get(currency, '#ffffff')
            # Add currency sign based on μₜ
            currency_sign = currency_signs.get(currency, "")
            currency_display = f"{currency}{currency_sign}" if currency_sign else currency

            header_cells.append(
                html.Th(currency_display,
                       style={
                           'background-color': color,
                           'color': '#000000',
                           'font-weight': 'bold',
                           'text-align': 'center',
                           'padding': '10px',
                           'border': '1px solid #444'
                       })
            )

        # Create table rows (show top 5 contributing pairs for each currency)
        max_rows = 5
        table_rows = []

        for row_idx in range(max_rows):
            row_cells = []
            for currency in currencies:
                contributions = pair_contributions.get(currency, [])
                if row_idx < len(contributions):
                    pair, contribution = contributions[row_idx]

                    # Calculate contribution percentage relative to total CSSD for this currency
                    total_contrib = sum([c[1] for c in contributions]) if contributions else 1
                    contribution_pct = (contribution / total_contrib) * 100 if total_contrib > 0 else 0

                    # Create a bar representation based on percentage contribution
                    bar_width = contribution_pct if contribution_pct <= 100 else 100

                    cell_content = html.Div([
                        html.Div(pair, style={'font-size': '11px', 'margin-bottom': '2px'}),
                        html.Div([
                            html.Div(
                                style={
                                    'background-color': CURRENCY_COLORS.get(currency, '#ffffff'),
                                    'height': '8px',
                                    'width': f'{bar_width}%',
                                    'opacity': '0.7'
                                }
                            )
                        ], style={'background-color': '#333', 'height': '8px', 'border-radius': '2px'}),
                        html.Div(f'{contribution_pct:.1f}%',
                               style={'font-size': '10px', 'color': '#888', 'margin-top': '2px'})
                    ])
                else:
                    cell_content = ""

                row_cells.append(
                    html.Td(cell_content,
                           style={
                               'padding': '8px',
                               'border': '1px solid #444',
                               'text-align': 'center',
                               'vertical-align': 'top',
                               'background-color': '#2a2a2a'
                           })
                )
            table_rows.append(html.Tr(row_cells))

        # Create the table
        table = html.Table([
            html.Thead([html.Tr(header_cells)]),
            html.Tbody(table_rows)
        ], style={
            'width': '100%',
            'border-collapse': 'collapse',
            'margin-top': '10px',
            'font-family': 'Arial, sans-serif'
        })

        # Create currency weights section if available
        weights_section = []
        if currency_weights:
            weights_section = self._create_currency_weights_section(currency_weights, CURRENCY_COLORS, sharpe_optimized_allocation)

        return html.Div([
            html.P("Shows top 5 pairs contributing most to each currency's CSSD since start of day. Bar length and percentage show each pair's contribution to the currency's total CSSD.",
                  style={'color': '#888', 'font-size': '12px', 'margin-bottom': '10px'}),
            table
        ] + weights_section)

    def _create_currency_weights_section(self, currency_weights: dict, currency_colors: dict, sharpe_optimized_allocation: str = None) -> list:
        """
        Create MPT allocation format section with readonly editboxes for easy copying.

        Args:
            currency_weights: Dictionary with currency -> list of (pair, normalized_weight) tuples
            currency_colors: Dictionary with currency color mappings

        Returns:
            List of HTML components for the weights section
        """
        if not currency_weights:
            return []

        currencies = ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']

        # Create section header
        section_components = [
            html.Hr(style={'margin': '20px 0', 'border-color': '#444'}),
            html.H4("MPT Allocation Format (Copy-Ready):",
                   style={'margin-bottom': '15px', 'color': '#fff'})
        ]

        # Create readonly input boxes for each currency
        for currency in currencies:
            weights = currency_weights.get(currency, [])
            if not weights:
                continue

            color = currency_colors.get(currency, '#ffffff')

            # Handle "NA" case for currencies with top contributor > 50%
            if weights == "NA":
                mpt_string = "NA"
            else:
                # Create MPT format string in decimal format: "EURUSD:0.456,EURAUD:0.458,GBPUSD:-0.956"
                mpt_pairs = []
                for pair, weight in weights:
                    mpt_pairs.append(f"{pair}:{weight:.3f}")

                mpt_string = ",".join(mpt_pairs)

            # Create currency label and readonly input box
            currency_row = html.Div([
                html.Div([
                    html.Span(f"{currency}:",
                             style={
                                 'color': color,
                                 'font-weight': 'bold',
                                 'font-size': '14px',
                                 'display': 'inline-block',
                                 'width': '50px',
                                 'margin-right': '10px'
                             })
                ], style={'display': 'inline-block', 'vertical-align': 'top', 'margin-top': '8px'}),

                html.Div([
                    dcc.Input(
                        value=mpt_string,
                        readOnly=True,
                        style={
                            'width': '100%',
                            'padding': '8px 12px',
                            'background-color': '#2a2a2a',
                            'border': f'2px solid {color}',
                            'border-radius': '4px',
                            'color': '#fff',
                            'font-family': 'Consolas, Monaco, monospace',
                            'font-size': '12px',
                            'cursor': 'text'
                        }
                    )
                ], style={'display': 'inline-block', 'width': 'calc(100% - 70px)'})

            ], style={
                'margin': '8px 0',
                'display': 'flex',
                'align-items': 'center'
            })

            section_components.append(currency_row)

        # Add Sharpe-optimized allocation if available
        if sharpe_optimized_allocation:
            # Add separator
            section_components.append(
                html.Hr(style={'margin': '15px 0', 'border-color': '#555'})
            )

            # Add Sharpe-optimized allocation row
            sharpe_row = html.Div([
                html.Div([
                    html.Span("TOP:",
                             style={
                                 'color': '#ffffff',
                                 'font-weight': 'bold',
                                 'font-size': '14px',
                                 'display': 'inline-block',
                                 'width': '50px',
                                 'margin-right': '10px'
                             })
                ], style={'display': 'inline-block', 'vertical-align': 'top', 'margin-top': '8px'}),

                html.Div([
                    dcc.Input(
                        value=sharpe_optimized_allocation,
                        readOnly=True,
                        style={
                            'width': '100%',
                            'padding': '8px 12px',
                            'background-color': '#2a2a2a',
                            'border': '2px solid #ffffff',
                            'border-radius': '4px',
                            'color': '#fff',
                            'font-family': 'Consolas, Monaco, monospace',
                            'font-size': '12px',
                            'cursor': 'text'
                        }
                    )
                ], style={'display': 'inline-block', 'width': 'calc(100% - 70px)'})

            ], style={
                'margin': '8px 0',
                'display': 'flex',
                'align-items': 'center'
            })

            section_components.append(sharpe_row)

            # Add description for Sharpe optimization
            section_components.append(
                html.P("TOP: Sharpe-optimized allocation using the top contributing pair from each currency (log returns)",
                      style={'color': '#888', 'font-size': '12px', 'margin-top': '10px', 'font-style': 'italic'})
            )

        # Add general description
        section_components.append(
            html.P("Click in any box to select all text for easy copying. Format: PAIR1:weight,PAIR2:weight,PAIR3:weight",
                  style={'color': '#888', 'font-size': '12px', 'margin-top': '15px', 'font-style': 'italic'})
        )

        return section_components

    def _create_initial_warning_chart(self):
        """Create initial warning system chart with instructions"""
        fig = go.Figure()
        fig.add_annotation(
            text="Portfolio Entry Warning System<br><br>Click 'Update Warning System' to load real-time data<br>or wait for automatic updates every minute",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=18, color="white"),
            bgcolor="rgba(74, 144, 226, 0.8)",
            bordercolor="white",
            borderwidth=2,
            borderpad=20
        )
        fig.update_layout(
            title="Portfolio Entry Warning System - Dispersion Breakout Strategy",
            template="plotly_dark",
            height=900,
            width=1400,
            autosize=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, title=""),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, title=""),
            showlegend=False
        )
        return fig

    def _create_warning_status(self, message: str):
        """Create warning system status message"""
        return html.Div(
            message,
            style={
                'padding': '10px',
                'backgroundColor': '#2d3748',
                'borderRadius': '5px',
                'color': 'white',
                'fontSize': '12px',
                'border': '1px solid #444'
            }
        )

    def _create_empty_currency_cssd_chart(self):
        """Create empty currency rolling CSSD dispersion chart when data is not available"""
        fig = go.Figure()
        fig.add_annotation(
            text="Currency Rolling CSSD Dispersion Chart<br><br>Waiting for market data...<br>Chart will update automatically every minute",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=16, color="white"),
            bgcolor="rgba(68, 68, 68, 0.8)",
            bordercolor="white",
            borderwidth=1
        )
        fig.update_layout(
            title="Rolling Dispersion of CSSD by Currency",
            template="plotly_dark",
            height=1000,  # 2x the original height (was 500)
            width=1400,
            autosize=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, title="Time"),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, title="Rolling CSSD Dispersion")
        )
        return fig

    def _create_portfolio_card(self, portfolio_data: Dict, index: int, lot_size: float) -> html.Div:
        """Create a portfolio display card"""
        strategy = portfolio_data.get('strategy', 'Unknown')
        pairs = portfolio_data.get('pairs', [])
        weights = portfolio_data.get('weights', [])
        metrics = portfolio_data.get('metrics', {})
        
        # Calculate lot sizes
        lot_sizes = [w * lot_size for w in weights]
        
        # Create weight/lot size table with currency-based colors
        from config import get_pair_color

        # Create conditional styling for each pair based on currency colors
        style_data_conditional = []
        for pair, weight in zip(pairs, weights):
            pair_color = get_pair_color(pair, weight)
            style_data_conditional.append({
                'if': {'filter_query': f'{{Pair}} = {pair}'},
                'backgroundColor': pair_color,
                'color': 'white' if pair_color != '#ffff00' else 'black',  # Black text for yellow background
            })

        weight_table = dash_table.DataTable(
            data=[
                {
                    'Pair': pair,
                    'Weight': f"{weight:+.3f}",
                    'Lot Size': f"{lot:+.2f}",
                    'Direction': 'Long' if weight > 0 else 'Short'
                }
                for pair, weight, lot in zip(pairs, weights, lot_sizes)
            ],
            columns=[
                {'name': 'Pair', 'id': 'Pair'},
                {'name': 'Weight', 'id': 'Weight'},
                {'name': 'Lot Size', 'id': 'Lot Size'},
                {'name': 'Direction', 'id': 'Direction'}
            ],
            style_cell={'textAlign': 'center', 'fontSize': '12px'},
            style_data_conditional=style_data_conditional
        )



        # Create MPT allocation format string
        mpt_allocation = ",".join([f"{pair}:{weight:.3f}" for pair, weight in zip(pairs, weights)])

        # Create read-only text area for MPT allocation format
        mpt_text_area = dcc.Textarea(
            value=mpt_allocation,
            readOnly=True,
            style={
                'width': '100%',
                'height': '60px',
                'fontSize': '12px',
                'fontFamily': 'monospace',
                'backgroundColor': '#2c3e50',
                'color': '#ecf0f1',
                'border': '1px solid #444',
                'borderRadius': '4px',
                'padding': '8px',
                'resize': 'none'
            },
            placeholder="MPT Allocation Format"
        )
        
        return html.Div([
            html.H4(f"Portfolio {index} - {strategy.replace('_', ' ').title()}"),
            html.Div([
                html.Div([
                    html.Strong("Return: "),
                    html.Span(f"{metrics.get('expected_return', 0):.6f}")
                ]),
                html.Div([
                    html.Strong("Sharpe Ratio: "),
                    html.Span(f"{metrics.get('sharpe_ratio', 0):.3f}")
                ]),
                html.Div([
                    html.Strong("Sortino Ratio: "),
                    html.Span(f"{metrics.get('sortino_ratio', 0):.3f}")
                ]),
                html.Div([
                    html.Strong("Omega Ratio: "),
                    html.Span(f"{metrics.get('omega_ratio', 0):.3f}")
                ]),
                html.Div([
                    html.Strong("Calmar Ratio: "),
                    html.Span(f"{metrics.get('calmar_ratio', 0):.3f}")
                ]),
                html.Div([
                    html.Strong("Volatility: "),
                    html.Span(f"{metrics.get('volatility', 0):.6f}")
                ]),
                html.Div([
                    html.Strong("Max Drawdown: "),
                    html.Span(f"{metrics.get('max_drawdown', 0):.6f}")
                ])
            ], className="metrics-summary"),
            weight_table,
            html.Div([
                html.Label("MPT Allocation Format (Copy-Ready):",
                          style={'font-weight': 'bold', 'margin-top': '10px', 'margin-bottom': '5px', 'display': 'block'}),
                mpt_text_area
            ]),
            html.Div([
                html.Button("View Details",
                           id={'type': 'portfolio-btn', 'index': index-1},
                           className="portfolio-select-btn",
                           style={'margin-top': '10px', 'width': '48%', 'margin-right': '2%'}),
                html.Button("Send to MT5",
                           id={'type': 'mt5-trade-btn', 'index': index-1},
                           className="mt5-trade-btn",
                           style={'margin-top': '10px', 'width': '48%', 'background-color': '#28a745', 'color': 'white'})
            ], style={'display': 'flex', 'justify-content': 'space-between'})
        ], className="portfolio-card", id=f"portfolio-card-{index}")
    
    def _create_portfolio_comparison_chart(self, portfolios_data: List[Dict]) -> go.Figure:
        """Create portfolio comparison scatter plot"""
        fig = go.Figure()
        
        for portfolio in portfolios_data:
            metrics = portfolio.get('metrics', {})
            strategy = portfolio.get('strategy', 'Unknown')
            
            fig.add_trace(go.Scatter(
                x=[metrics.get('volatility', 0)],
                y=[metrics.get('expected_return', 0)],
                mode='markers',
                name=strategy.replace('_', ' ').title(),
                marker=dict(size=15),
                text=f"Sharpe: {metrics.get('sharpe_ratio', 0):.3f}",
                hovertemplate="<b>%{fullData.name}</b><br>" +
                            "Volatility: %{x:.6f}<br>" +
                            "Return: %{y:.6f}<br>" +
                            "%{text}<extra></extra>"
            ))
        
        fig.update_layout(
            title="Portfolio Risk-Return Profile",
            xaxis_title="Volatility",
            yaxis_title="Expected Return",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            autosize=False,
            margin=dict(l=50, r=50, t=80, b=50)
        )
        
        return fig
    
    def _create_risk_metrics_chart(self, portfolios_data: List[Dict]) -> go.Figure:
        """Create risk metrics comparison chart"""
        strategies = []
        sharpe_ratios = []
        sortino_ratios = []
        omega_ratios = []
        calmar_ratios = []

        for portfolio in portfolios_data:
            metrics = portfolio.get('metrics', {})
            strategies.append(portfolio.get('strategy', 'Unknown').replace('_', ' ').title())
            sharpe_ratios.append(metrics.get('sharpe_ratio', 0))
            sortino_ratios.append(metrics.get('sortino_ratio', 0))
            omega_ratios.append(metrics.get('omega_ratio', 0))

            # Handle potential infinity values in Calmar ratio
            calmar_ratio = metrics.get('calmar_ratio', 0)
            if calmar_ratio == float('inf') or calmar_ratio == float('-inf'):
                calmar_ratio = 0  # Set to 0 for display purposes
            calmar_ratios.append(calmar_ratio)

        fig = go.Figure()

        # Use 4 distinct colors for the ratios
        ratio_colors = {
            'sharpe': '#1f77b4',    # Blue
            'sortino': '#ff7f0e',   # Orange
            'omega': '#2ca02c',     # Green
            'calmar': '#d62728'     # Red
        }

        fig.add_trace(go.Bar(
            name='Sharpe Ratio',
            x=strategies,
            y=sharpe_ratios,
            marker_color=ratio_colors['sharpe']
        ))

        fig.add_trace(go.Bar(
            name='Sortino Ratio',
            x=strategies,
            y=sortino_ratios,
            marker_color=ratio_colors['sortino']
        ))

        fig.add_trace(go.Bar(
            name='Omega Ratio',
            x=strategies,
            y=omega_ratios,
            marker_color=ratio_colors['omega']
        ))

        fig.add_trace(go.Bar(
            name='Calmar Ratio',
            x=strategies,
            y=calmar_ratios,
            marker_color=ratio_colors['calmar']
        ))

        fig.update_layout(
            title="Risk-Adjusted Return Metrics",
            xaxis_title="Strategy",
            yaxis_title="Ratio",
            barmode='group',
            template=CHART_THEME,
            height=CHART_HEIGHT,
            autosize=False,
            margin=dict(l=50, r=50, t=80, b=50)
        )

        return fig
    
    def _create_weight_allocation_chart(self, portfolios_data: List[Dict]) -> go.Figure:
        """Create weight allocation chart with currency-based colors"""
        from config import get_pair_color

        # Calculate lot sizes for display
        portfolio_info = []
        for portfolio in portfolios_data:
            pairs = portfolio.get('pairs', [])
            weights = portfolio.get('weights', [])

            # Calculate lot sizes (assuming 1.0 total lot size for display)
            lot_sizes = [abs(w) * 1.0 for w in weights]  # Simple proportional allocation

            # Create info string for this portfolio
            pair_info = []
            for pair, weight, lot in zip(pairs, weights, lot_sizes):
                direction = "L" if weight > 0 else "S"
                pair_info.append(f"{pair}({direction}:{lot:.2f})")

            portfolio_info.append(" | ".join(pair_info))

        fig = make_subplots(
            rows=len(portfolios_data),
            cols=1,
            subplot_titles=[f"Portfolio {i+1} - {p.get('strategy', '').replace('_', ' ').title()}<br><span style='font-size:10px'>{info}</span>"
                          for i, (p, info) in enumerate(zip(portfolios_data, portfolio_info))],
            vertical_spacing=0.15  # Increased spacing for subtitle
        )

        for i, portfolio in enumerate(portfolios_data):
            pairs = portfolio.get('pairs', [])
            weights = portfolio.get('weights', [])
            lot_sizes = [abs(w) * 1.0 for w in weights]  # Calculate lot sizes

            # Get currency-based colors for each pair based on weight direction
            colors = []
            for pair, weight in zip(pairs, weights):
                # Use weight as proxy for log return direction (positive weight = positive return)
                color = get_pair_color(pair, weight)
                colors.append(color)

            fig.add_trace(
                go.Bar(
                    x=pairs,
                    y=weights,
                    name=f"Portfolio {i+1}",
                    marker_color=colors,
                    showlegend=False,
                    hovertemplate="<b>%{x}</b><br>" +
                                "Weight: %{y:.3f}<br>" +
                                "Lot Size: %{customdata:.2f}<br>" +
                                "Direction: %{text}<br>" +
                                "<extra></extra>",
                    customdata=lot_sizes,
                    text=["Long" if w > 0 else "Short" for w in weights]
                ),
                row=i+1, col=1
            )

        fig.update_layout(
            title="Portfolio Weight Allocations (Currency-Based Colors)",
            template=CHART_THEME,
            height=CHART_HEIGHT * len(portfolios_data) // 2 + 50,  # Extra height for subtitles
            autosize=False,
            margin=dict(l=50, r=50, t=80, b=50)
        )

        return fig
    
    def _create_correlation_matrix_chart(self, returns_data: Dict) -> go.Figure:
        """Create correlation matrix heatmap"""
        # This would use actual returns data to create correlation matrix
        # For now, create a placeholder
        fig = go.Figure()

        fig.add_annotation(
            text="Click on a portfolio to see correlation matrix<br>for its 6 currency pairs",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16)
        )

        fig.update_layout(
            title="Currency Pair Correlation Matrix",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            autosize=False,
            margin=dict(l=50, r=50, t=80, b=50)
        )

        return fig

    def _create_portfolio_returns_chart(self, portfolio_data: Dict, time_period: str = '24h') -> go.Figure:
        """Create chart showing weighted portfolio cumulative returns with RSI subplot for the selected time period"""
        try:
            pairs = portfolio_data.get('pairs', [])
            weights = portfolio_data.get('weights', [])
            strategy = portfolio_data.get('strategy', 'Unknown')

            # For periods > 24h, we need to fetch full period data for the chart
            # to show cumulative returns from the beginning of the period
            if time_period != '24h' and time_period.endswith('h'):
                hours = int(time_period[:-1])
                if hours > 24:
                    # Fetch full period data for chart display
                    try:
                        logger.info(f"Fetching full period data for chart display: {time_period}")
                        full_period_data = self.mt5_connector.fetch_daily_data(
                            current_day_only=False,
                            time_period=time_period
                        )
                        if full_period_data:
                            chart_returns = self.returns_calculator.calculate_log_returns(full_period_data)
                            logger.info(f"Chart data range: {chart_returns.index.min()} to {chart_returns.index.max()}")
                        else:
                            chart_returns = self.current_returns
                    except Exception as e:
                        logger.warning(f"Failed to fetch full period data for chart: {e}, using current returns")
                        chart_returns = self.current_returns
                else:
                    chart_returns = self.current_returns
            else:
                chart_returns = self.current_returns

            # Check if we have valid data
            if chart_returns is None or chart_returns.empty:
                logger.warning("No chart returns data available")
                return self._create_empty_figure("No returns data available for selected portfolio")

            if all(pair in chart_returns.columns for pair in pairs) and len(weights) == len(pairs):
                # Import required modules for subplots and RSI
                from plotly.subplots import make_subplots
                from utils import calculate_rsi

                # Create subplots: main chart (80%) and RSI subplot (20%)
                fig = make_subplots(
                    rows=2, cols=1,
                    subplot_titles=["Portfolio Cumulative Returns", "RSI"],
                    vertical_spacing=0.08,
                    row_heights=[0.8, 0.2],
                    specs=[[{"secondary_y": False}], [{"secondary_y": False}]]
                )

                # Get returns for this portfolio's pairs
                portfolio_returns = chart_returns[pairs]

                # For periods > 24h, filter out weekend data from chart display
                if time_period != '24h' and time_period.endswith('h'):
                    hours = int(time_period[:-1])
                    if hours > 24:
                        # Filter out weekends (Saturday=5, Sunday=6) from chart data
                        weekday_mask = portfolio_returns.index.weekday < 5
                        portfolio_returns = portfolio_returns[weekday_mask]
                        logger.info(f"Filtered weekend data for chart: {len(portfolio_returns)} weekday data points remaining")

                # Calculate weighted portfolio returns (same as metrics calculation)
                portfolio_return_series = (portfolio_returns * weights).sum(axis=1)

                # Calculate cumulative returns: (1 + r1) * (1 + r2) * ... - 1
                cumulative_returns = (1 + portfolio_return_series).cumprod() - 1

                # Calculate RSI for the cumulative returns
                rsi_values = calculate_rsi(cumulative_returns, window=14)

                # For periods > 24h, create a continuous x-axis without weekend gaps
                if time_period != '24h' and time_period.endswith('h'):
                    hours = int(time_period[:-1])
                    if hours > 24:
                        # Create a continuous index for weekdays only
                        # This removes weekend gaps from the x-axis display
                        continuous_index = list(range(len(cumulative_returns)))  # Convert range to list
                        x_values = continuous_index
                        # Create custom tick labels showing only the actual dates (weekdays)
                        actual_dates = cumulative_returns.index
                        # Sample dates for x-axis labels (show every few hours to avoid crowding)
                        sample_interval = max(1, len(actual_dates) // 10)  # Show ~10 labels max
                        tick_positions = list(range(0, len(actual_dates), sample_interval))
                        tick_labels = [actual_dates[i].strftime('%b %d\n%H:%M') for i in tick_positions]
                        custom_x_axis = True
                    else:
                        x_values = cumulative_returns.index
                        custom_x_axis = False
                else:
                    x_values = cumulative_returns.index
                    custom_x_axis = False

                # Log the data range being used for the chart
                data_start = cumulative_returns.index.min()
                data_end = cumulative_returns.index.max()
                logger.info(f"Chart displaying data from {data_start} to {data_end} for {time_period} period")

                # Add the portfolio trace to main subplot
                if custom_x_axis:
                    # Use continuous x-axis for periods > 24h to remove weekend gaps
                    hover_text = [f"<b>Portfolio Cumulative Returns</b><br>Time: {actual_dates[i].strftime('%Y-%m-%d %H:%M')}<br>Cumulative Return: {cumulative_returns.values[i]:.6f}<br>Pairs: {', '.join(pairs)}<br>Weights: {', '.join([f'{w:.3f}' for w in weights])}"
                                 for i in range(len(cumulative_returns))]

                    fig.add_trace(go.Scatter(
                        x=x_values,
                        y=cumulative_returns.values,
                        mode='lines',
                        name=f'Portfolio Returns ({len(pairs)} pairs)',
                        line=dict(color=COLORS['primary'], width=3),
                        hovertext=hover_text,
                        hoverinfo='text',
                        showlegend=False
                    ), row=1, col=1)
                else:
                    # Use normal datetime x-axis for 24h and shorter periods
                    fig.add_trace(go.Scatter(
                        x=x_values,
                        y=cumulative_returns.values,
                        mode='lines',
                        name=f'Portfolio Returns ({len(pairs)} pairs)',
                        line=dict(color=COLORS['primary'], width=3),
                        hovertemplate="<b>Portfolio Cumulative Returns</b><br>" +
                                    "Time: %{x}<br>" +
                                    "Cumulative Return: %{y:.6f}<br>" +
                                    f"Pairs: {', '.join(pairs)}<br>" +
                                    f"Weights: {', '.join([f'{w:.3f}' for w in weights])}<extra></extra>",
                        showlegend=False
                    ), row=1, col=1)

                # Add linear regression channel (120 length)
                regression_data = self._calculate_linear_regression_channel(cumulative_returns, length=120)
                if regression_data['x_values'] is not None and len(regression_data['x_values']) > 0:
                    # Map regression x_values to the same format as the main chart
                    if custom_x_axis:
                        # For custom x-axis, map the regression x_values to continuous indices
                        reg_start_idx = len(cumulative_returns) - len(regression_data['x_values'])
                        reg_x_values = list(range(reg_start_idx, len(cumulative_returns)))
                    else:
                        reg_x_values = regression_data['x_values']

                    # Add regression channel lines
                    # 2SD High line
                    fig.add_trace(go.Scatter(
                        x=reg_x_values,
                        y=regression_data['high_2sd'],
                        mode='lines',
                        name='Regression +2SD',
                        line=dict(color='rgba(255, 0, 0, 0.5)', width=1, dash='dash'),
                        showlegend=False,
                        hovertemplate="<b>Regression +2SD</b><br>Value: %{y:.6f}<extra></extra>"
                    ), row=1, col=1)

                    # 1SD High line
                    fig.add_trace(go.Scatter(
                        x=reg_x_values,
                        y=regression_data['high_1sd'],
                        mode='lines',
                        name='Regression +1SD',
                        line=dict(color='rgba(255, 255, 0, 0.6)', width=1, dash='dot'),
                        showlegend=False,
                        hovertemplate="<b>Regression +1SD</b><br>Value: %{y:.6f}<extra></extra>"
                    ), row=1, col=1)

                    # Mid line (regression line)
                    fig.add_trace(go.Scatter(
                        x=reg_x_values,
                        y=regression_data['mid'],
                        mode='lines',
                        name='Regression Mid',
                        line=dict(color='rgba(255, 255, 255, 0.8)', width=2),
                        showlegend=False,
                        hovertemplate="<b>Regression Mid</b><br>Value: %{y:.6f}<br>" +
                                    f"R²: {regression_data['r_squared']:.4f}<br>" +
                                    f"Slope: {regression_data['slope']:.6f}<extra></extra>"
                    ), row=1, col=1)

                    # 1SD Low line
                    fig.add_trace(go.Scatter(
                        x=reg_x_values,
                        y=regression_data['low_1sd'],
                        mode='lines',
                        name='Regression -1SD',
                        line=dict(color='rgba(255, 255, 0, 0.6)', width=1, dash='dot'),
                        showlegend=False,
                        hovertemplate="<b>Regression -1SD</b><br>Value: %{y:.6f}<extra></extra>"
                    ), row=1, col=1)

                    # 2SD Low line
                    fig.add_trace(go.Scatter(
                        x=reg_x_values,
                        y=regression_data['low_2sd'],
                        mode='lines',
                        name='Regression -2SD',
                        line=dict(color='rgba(255, 0, 0, 0.5)', width=1, dash='dash'),
                        showlegend=False,
                        hovertemplate="<b>Regression -2SD</b><br>Value: %{y:.6f}<extra></extra>"
                    ), row=1, col=1)

                # Add RSI trace with dynamic coloring to second subplot
                dates_for_rsi = actual_dates if custom_x_axis else None
                self._add_colored_rsi_traces(fig, x_values, rsi_values, custom_x_axis, dates_for_rsi)

                # Add RSI reference lines (70, 50, 30)
                for level, color in [(70, 'red'), (50, 'white'), (30, 'green')]:
                    fig.add_hline(
                        y=level,
                        line=dict(color=color, width=1, dash='dot'),
                        opacity=0.7,
                        row=2, col=1
                    )

                # Create title with pair and lot size information
                lot_sizes = [abs(w) * 1.0 for w in weights]  # Calculate lot sizes for display
                pair_info = []
                for pair, weight, lot in zip(pairs, weights, lot_sizes):
                    direction = "L" if weight > 0 else "S"
                    pair_info.append(f"{pair}({direction}:{lot:.2f})")

                # Create time period description for title and axis
                if time_period == '24h':
                    period_desc = "Current Day"
                elif time_period.endswith('h'):
                    hours = int(time_period[:-1])
                    if hours < 24:
                        period_desc = f"Last {hours} Hours"
                    else:
                        days = hours // 24
                        if hours > 24:
                            period_desc = f"Full {days}-Day Period"
                        else:
                            period_desc = f"Last {days} Days"
                else:
                    period_desc = time_period.upper()

                title_with_pairs = f"Portfolio Cumulative Returns - {strategy.replace('_', ' ').title()}<br><span style='font-size:12px'>{' | '.join(pair_info)}</span>"

                # Configure layout for subplots
                layout_config = {
                    'title': title_with_pairs,
                    'template': CHART_THEME,
                    'height': 700,  # Increased height for subplots
                    'width': CHART_WIDTH,
                    'hovermode': 'x unified',
                    'showlegend': False  # Remove legend to allow chart to fully extend
                }

                fig.update_layout(**layout_config)

                # Update y-axis titles for subplots
                fig.update_yaxes(title_text="Portfolio Cumulative Returns", row=1, col=1)
                fig.update_yaxes(title_text="RSI", row=2, col=1)

                # Apply custom x-axis configuration for periods > 24h
                if custom_x_axis:
                    # Update x-axis for both subplots
                    fig.update_xaxes(
                        tickmode='array',
                        tickvals=tick_positions,
                        ticktext=tick_labels,
                        title=f"Trading Days ({period_desc} - Weekends Excluded)",
                        row=2, col=1  # Only show x-axis title on bottom subplot
                    )
                    fig.update_xaxes(showticklabels=False, row=1, col=1)  # Hide x-axis labels on top subplot
                else:
                    # Normal datetime x-axis
                    fig.update_xaxes(title=f"Time ({period_desc})", row=2, col=1)
                    fig.update_xaxes(showticklabels=False, row=1, col=1)  # Hide x-axis labels on top subplot

                return fig
            else:
                logger.warning(f"No returns data available for selected portfolio. Chart returns shape: {chart_returns.shape}, pairs: {pairs}")
                return self._create_empty_figure("No returns data available for selected portfolio")

        except Exception as e:
            logger.error(f"Error creating portfolio returns chart: {str(e)}")
            return self._create_empty_figure(f"Error creating chart: {str(e)}")

    def _create_portfolio_correlation_matrix(self, portfolio_data: Dict) -> go.Figure:
        """Create correlation matrix for selected portfolio's 6 pairs"""
        try:
            pairs = portfolio_data.get('pairs', [])
            strategy = portfolio_data.get('strategy', 'Unknown')

            if not self.current_returns.empty and all(pair in self.current_returns.columns for pair in pairs):
                # Calculate correlation matrix for the 6 pairs
                portfolio_returns = self.current_returns[pairs]
                correlation_matrix = portfolio_returns.corr()

                fig = go.Figure(data=go.Heatmap(
                    z=correlation_matrix.values,
                    x=correlation_matrix.columns,
                    y=correlation_matrix.index,
                    colorscale='RdBu',
                    zmid=0,
                    text=correlation_matrix.round(3).values,
                    texttemplate="%{text}",
                    textfont={"size": 10},
                    hoverongaps=False
                ))

                # Create title with pair information
                pair_list = ", ".join(pairs)
                title_with_pairs = f"Correlation Matrix - {strategy.replace('_', ' ').title()}<br><span style='font-size:12px'>Pairs: {pair_list}</span>"

                fig.update_layout(
                    title=title_with_pairs,
                    template=CHART_THEME,
                    height=CHART_HEIGHT,
                    xaxis_title="Currency Pairs",
                    yaxis_title="Currency Pairs"
                )

                return fig
            else:
                return self._create_empty_figure("No correlation data available for selected portfolio")

        except Exception as e:
            logger.error(f"Error creating correlation matrix: {str(e)}")
            return self._create_empty_figure(f"Error creating correlation matrix: {str(e)}")

    def _calculate_linear_regression_channel(self, y_data: pd.Series, length: int = 120) -> Dict:
        """
        Calculate linear regression channel with high/low/mid lines

        Args:
            y_data: Series of y values (cumulative returns)
            length: Length of the regression window (default 120)

        Returns:
            Dictionary containing regression lines data
        """
        if len(y_data) < length:
            length = len(y_data)

        if length < 2:
            return {'mid': [], 'high': [], 'low': [], 'x_values': []}

        # Use the last 'length' points for regression
        y_subset = y_data.iloc[-length:].values
        x_subset = np.arange(len(y_subset))

        # Calculate linear regression
        slope, intercept, r_value, p_value, std_err = stats.linregress(x_subset, y_subset)

        # Calculate regression line
        regression_line = slope * x_subset + intercept

        # Calculate residuals and standard deviation
        residuals = y_subset - regression_line
        std_dev = np.std(residuals)

        # Create channel lines
        high_line_1sd = regression_line + std_dev
        low_line_1sd = regression_line - std_dev
        high_line_2sd = regression_line + (2 * std_dev)
        low_line_2sd = regression_line - (2 * std_dev)

        # Map back to original index
        original_index = y_data.index[-length:]

        return {
            'mid': regression_line,
            'high_1sd': high_line_1sd,
            'low_1sd': low_line_1sd,
            'high_2sd': high_line_2sd,
            'low_2sd': low_line_2sd,
            'x_values': original_index,
            'slope': slope,
            'r_squared': r_value**2
        }

    def _create_empty_figure(self, message: str) -> go.Figure:
        """Create an empty figure with a message"""
        fig = go.Figure()
        
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        
        fig.update_layout(
            template=CHART_THEME,
            height=CHART_HEIGHT
        )
        
        return fig
    
    def _run_real_optimization(self, strategy: str, enable_diversification: bool = True, time_period: str = '24h') -> List[Dict]:
        """Run real portfolio optimization with MT5 data"""
        try:
            logger.info(f"Starting real optimization for strategy: {strategy}")

            # Ensure fresh MT5 connection and data
            if not self.mt5_connector.is_connected():
                if not self.mt5_connector.connect():
                    raise Exception("Failed to connect to MT5")

            # Force a brief delay to ensure MT5 data is current
            import time
            time.sleep(0.1)

            # Fetch market data for specified time period
            logger.info(f"Fetching fresh market data for time period: {time_period}...")
            market_data = self.mt5_connector.fetch_daily_data(
                current_day_only=(time_period == '24h'),
                time_period=time_period
            )

            if not market_data:
                raise Exception("No market data available")

            logger.info(f"Fetched {time_period} data for {len(market_data)} pairs")

            # Also fetch extended data for CVaR calculations (use more historical data for CVaR)
            if time_period in ['2h', '4h', '8h']:
                # For short periods, use 24h data for CVaR calculations
                extended_data = self.mt5_connector.fetch_daily_data(current_day_only=False, time_period='24h')
            else:
                # For longer periods, use current time period data
                extended_data = market_data
            self.extended_data = extended_data  # Store for CVaR calculations

            # Calculate log returns
            log_returns = self.returns_calculator.calculate_log_returns(market_data)

            if log_returns.empty:
                raise Exception("Failed to calculate log returns")

            logger.info(f"Calculated returns for {len(log_returns.columns)} pairs")

            # Debug: Log detailed statistics about the returns for this time period
            logger.info(f"Returns data shape: {log_returns.shape}")
            logger.info(f"Returns date range: {log_returns.index.min()} to {log_returns.index.max()}")
            logger.info(f"Sample returns stats - Mean: {log_returns.mean().mean():.6f}, Std: {log_returns.std().mean():.6f}")
            logger.info(f"Data freshness check - Latest timestamp: {log_returns.index.max()}")
            logger.info(f"Current time: {datetime.now(MARKET_TIMEZONE)}")

            # Additional debug: Check if data is actually fresh
            latest_data_time = log_returns.index.max()
            current_time = datetime.now(MARKET_TIMEZONE)
            time_diff = current_time - latest_data_time
            logger.info(f"Data age: {time_diff.total_seconds():.1f} seconds")

            if time_diff.total_seconds() > 300:  # More than 5 minutes old
                logger.warning(f"Data may be stale - {time_diff.total_seconds():.1f} seconds old")

            # Store returns for other charts
            self.current_returns = log_returns

            # Clear any cached data in the optimizer to ensure fresh calculations
            if hasattr(self.portfolio_optimizer, 'cached_combinations'):
                self.portfolio_optimizer.cached_combinations = None
            if hasattr(self.portfolio_optimizer, 'optimization_results'):
                self.portfolio_optimizer.optimization_results = []

            # Optimize portfolios
            strategies = ['max_sharpe', 'max_sortino', 'max_omega', 'max_calmar', 'min_variance'] if strategy == 'all' else [strategy]
            portfolios = self.portfolio_optimizer.optimize_portfolios(
                log_returns,
                strategies,
                enable_diversification=enable_diversification
            )

            if not portfolios:
                raise Exception("Portfolio optimization failed")

            logger.info(f"Optimized {len(portfolios)} portfolios")

            # Convert portfolios to dashboard format
            dashboard_portfolios = []

            for portfolio in portfolios:
                # Get directional weights
                base_weights = portfolio.get_weight_dict()
                directional_weights = self.weight_manager.assign_weight_directions(base_weights, log_returns)
                normalized_weights = self.weight_manager.normalize_absolute_weights(directional_weights)

                dashboard_portfolio = {
                    'strategy': portfolio.strategy,
                    'pairs': portfolio.pairs,
                    'weights': [normalized_weights[pair] for pair in portfolio.pairs],
                    'metrics': portfolio.metrics
                }
                dashboard_portfolios.append(dashboard_portfolio)

            # Store portfolios
            self.current_portfolios = dashboard_portfolios

            logger.info(f"Successfully prepared {len(dashboard_portfolios)} portfolios for dashboard")
            return dashboard_portfolios

        except Exception as e:
            logger.error(f"Real optimization failed: {str(e)}")
            # Fall back to sample data if optimization fails
            return self._generate_sample_portfolios(strategy)

    def _refresh_portfolio_metrics(self, time_period: str = '24h') -> List[Dict]:
        """Refresh metrics for existing portfolios with new data"""
        try:
            logger.info("Refreshing portfolio metrics with new data")

            # Check if we have existing portfolios
            if not self.current_portfolios:
                logger.warning(f"No existing portfolios to refresh, running full optimization for {time_period}")
                return self._run_real_optimization('all', True, time_period)

            # Connect to MT5 if not connected
            if not self.mt5_connector.is_connected():
                if not self.mt5_connector.connect():
                    raise Exception("Failed to connect to MT5")

            # Fetch fresh market data (current day only for metrics)
            logger.info("Fetching fresh market data...")
            market_data = self.mt5_connector.fetch_daily_data(current_day_only=True)

            if not market_data:
                raise Exception("No market data available")

            logger.info(f"Fetched fresh current day data for {len(market_data)} pairs")

            # Also fetch extended data for CVaR calculations
            extended_data = self.mt5_connector.fetch_daily_data(current_day_only=False)
            self.extended_data = extended_data  # Store for CVaR calculations

            # Calculate fresh log returns
            log_returns = self.returns_calculator.calculate_log_returns(market_data)

            if log_returns.empty:
                raise Exception("Failed to calculate log returns")

            logger.info(f"Calculated fresh returns for {len(log_returns.columns)} pairs")

            # Update stored returns
            self.current_returns = log_returns

            # Recalculate metrics for existing portfolios
            refreshed_portfolios = []

            for portfolio in self.current_portfolios:
                try:
                    # Keep the same pairs and strategy
                    pairs = portfolio['pairs']
                    strategy = portfolio['strategy']
                    weights = portfolio['weights']

                    # Ensure all pairs are available in new data
                    available_pairs = [pair for pair in pairs if pair in log_returns.columns]
                    if len(available_pairs) != len(pairs):
                        logger.warning(f"Some pairs not available in fresh data: {set(pairs) - set(available_pairs)}")
                        continue

                    # Get returns for this portfolio's pairs
                    portfolio_returns = log_returns[pairs]

                    # Calculate portfolio returns using existing weights
                    portfolio_return_series = (portfolio_returns * weights).sum(axis=1)

                    # Calculate fresh metrics manually (without optimization)
                    fresh_metrics = self._calculate_portfolio_metrics_simple(pairs, weights, portfolio_return_series)

                    # Create refreshed portfolio with same pairs/weights but fresh metrics
                    refreshed_portfolio = {
                        'strategy': strategy,
                        'pairs': pairs,
                        'weights': weights,
                        'metrics': fresh_metrics
                    }

                    refreshed_portfolios.append(refreshed_portfolio)
                    logger.info(f"Refreshed metrics for {strategy} portfolio")

                except Exception as e:
                    logger.error(f"Failed to refresh portfolio {portfolio.get('strategy', 'unknown')}: {str(e)}")
                    continue

            if not refreshed_portfolios:
                logger.warning("No portfolios could be refreshed, running full optimization")
                return self._run_real_optimization('all')

            # Update stored portfolios
            self.current_portfolios = refreshed_portfolios

            logger.info(f"Successfully refreshed {len(refreshed_portfolios)} portfolios")
            return refreshed_portfolios

        except Exception as e:
            logger.error(f"Portfolio refresh failed: {str(e)}")
            # Fall back to existing portfolios if refresh fails
            return self.current_portfolios if self.current_portfolios else []

    def _calculate_portfolio_metrics_simple(self, pairs: List[str], weights: List[float], portfolio_returns: pd.Series) -> Dict:
        """Calculate portfolio metrics without running optimization - matching original method"""
        from config import USE_CVAR_FOR_VOLATILITY, CVAR_CONFIDENCE_LEVEL
        from risk_calculator import calculate_var_cvar_numba

        try:
            # Basic return statistics (raw, not annualized)
            portfolio_return_mean = portfolio_returns.mean()  # Average per-period return
            portfolio_return_total = ((1 + portfolio_returns).cumprod() - 1).iloc[-1] if len(portfolio_returns) > 0 else 0  # Total cumulative return
            portfolio_std = portfolio_returns.std()
            portfolio_variance = portfolio_std ** 2

            # Choose volatility measure based on configuration
            if USE_CVAR_FOR_VOLATILITY:
                # Use CVaR as volatility measure - try to use extended data if available
                cvar_returns = portfolio_returns

                # If we have extended data, use it for CVaR calculation
                if hasattr(self, 'extended_data') and self.extended_data:
                    try:
                        # Get extended returns for this portfolio's pairs
                        extended_log_returns = self.returns_calculator.calculate_log_returns(self.extended_data)
                        if not extended_log_returns.empty and all(pair in extended_log_returns.columns for pair in pairs):
                            extended_portfolio_returns = extended_log_returns[pairs]
                            extended_portfolio_return_series = (extended_portfolio_returns * weights).sum(axis=1)
                            cvar_returns = extended_portfolio_return_series
                            logger.debug(f"Using extended data for CVaR: {len(cvar_returns)} vs {len(portfolio_returns)} points")
                    except Exception as e:
                        logger.debug(f"Could not use extended data for CVaR: {str(e)}")

                _, portfolio_cvar = calculate_var_cvar_numba(cvar_returns.values, CVAR_CONFIDENCE_LEVEL)
                portfolio_volatility = portfolio_cvar
            else:
                # Use standard deviation as volatility measure
                portfolio_volatility = portfolio_std

            # Sharpe ratio (using chosen volatility measure)
            sharpe_ratio = portfolio_return_mean / portfolio_volatility if portfolio_volatility > 0 else 0

            # Sortino ratio (downside deviation)
            downside_returns = portfolio_returns[portfolio_returns < 0]
            downside_deviation = downside_returns.std() if len(downside_returns) > 0 else 0
            sortino_ratio = portfolio_return_mean / downside_deviation if downside_deviation > 0 else 0

            # Omega ratio
            threshold = 0.0
            gains = portfolio_returns[portfolio_returns > threshold] - threshold
            losses = threshold - portfolio_returns[portfolio_returns < threshold]
            total_gains = gains.sum() if len(gains) > 0 else 0
            total_losses = losses.sum() if len(losses) > 0 else 0
            omega_ratio = total_gains / total_losses if total_losses > 0 else (float('inf') if total_gains > 0 else 1.0)

            # Maximum drawdown
            cumulative_returns = (1 + portfolio_returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0

            # Calmar ratio
            calmar_ratio = portfolio_return_mean / max_drawdown if max_drawdown > 0 else float('inf')

            # Information ratio (using chosen volatility measure)
            information_ratio = portfolio_return_mean / portfolio_volatility if portfolio_volatility > 0 else 0

            return {
                'expected_return': float(portfolio_return_total),  # Use total cumulative return for display
                'volatility': float(portfolio_volatility),  # Now uses CVaR if configured
                'variance': float(portfolio_variance),
                'sharpe_ratio': float(sharpe_ratio),
                'sortino_ratio': float(sortino_ratio),
                'omega_ratio': float(omega_ratio),
                'calmar_ratio': float(calmar_ratio),
                'information_ratio': float(information_ratio),
                'max_drawdown': float(max_drawdown),
                'std_volatility': float(portfolio_std),  # Keep standard deviation for reference
                'cvar_volatility': float(portfolio_cvar) if USE_CVAR_FOR_VOLATILITY else None
            }

        except Exception as e:
            logger.error(f"Error calculating simple metrics: {str(e)}")
            # Return default metrics if calculation fails
            return {
                'expected_return': 0.0,
                'volatility': 0.0,
                'variance': 0.0,
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'omega_ratio': 0.0,
                'calmar_ratio': 0.0,
                'information_ratio': 0.0,
                'max_drawdown': 0.0
            }

    def _generate_sample_portfolios(self, strategy: str) -> List[Dict]:
        """Generate sample portfolio data for fallback"""
        sample_portfolios = [
            {
                'strategy': 'max_sharpe',
                'pairs': ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'NZDUSD'],
                'weights': [0.25, 0.20, -0.15, 0.18, 0.22, -0.10],
                'metrics': {
                    'sharpe_ratio': 1.45,
                    'sortino_ratio': 1.82,
                    'omega_ratio': 1.65,
                    'calmar_ratio': 2.15,
                    'expected_return': 0.0012,
                    'volatility': 0.0083,
                    'max_drawdown': 0.056
                }
            }
        ]

        if strategy == 'all':
            return sample_portfolios
        else:
            return [p for p in sample_portfolios if p['strategy'] == strategy]
    
    def _add_colored_rsi_traces(self, fig, x_values, rsi_values, custom_x_axis=False, actual_dates=None):
        """
        Add RSI traces with dynamic color coding based on RSI values

        Args:
            fig: Plotly figure object
            x_values: X-axis values
            rsi_values: RSI values series
            custom_x_axis: Whether using custom x-axis
            actual_dates: Actual dates for custom x-axis hover
        """
        if rsi_values.empty or len(rsi_values) == 0:
            return

        # Remove NaN values
        valid_mask = ~rsi_values.isna()
        if not valid_mask.any():
            return

        rsi_clean = rsi_values[valid_mask]
        if custom_x_axis and actual_dates is not None and len(actual_dates) > 0:
            x_clean = [x_values[i] for i in range(len(x_values)) if valid_mask.iloc[i]]
            dates_clean = [actual_dates[i] for i in range(len(actual_dates)) if valid_mask.iloc[i]]
        else:
            if not custom_x_axis:
                x_clean = rsi_values.index[valid_mask]
            else:
                x_clean = [x_values[i] for i in range(len(x_values)) if valid_mask.iloc[i]]
            dates_clean = None

        # Create segments based on RSI values
        segments = []
        current_segment = {'x': [], 'y': [], 'color': 'yellow', 'dates': []}

        for i, (x_val, rsi_val) in enumerate(zip(x_clean, rsi_clean.values)):
            # Determine color based on RSI value
            if rsi_val > 70:
                color = 'blue'  # Overbought
            elif rsi_val < 30:
                color = 'red'   # Oversold
            else:
                color = 'yellow'  # Neutral

            # If color changes, end current segment and start new one
            if current_segment['color'] != color and current_segment['x']:
                # Add connecting point to previous segment
                current_segment['x'].append(x_val)
                current_segment['y'].append(rsi_val)
                if dates_clean:
                    current_segment['dates'].append(dates_clean[i])

                segments.append(current_segment.copy())

                # Start new segment with the connecting point
                current_segment = {
                    'x': [x_val],
                    'y': [rsi_val],
                    'color': color,
                    'dates': [dates_clean[i]] if dates_clean else []
                }
            else:
                # Continue current segment or start first segment
                current_segment['x'].append(x_val)
                current_segment['y'].append(rsi_val)
                current_segment['color'] = color
                if dates_clean:
                    current_segment['dates'].append(dates_clean[i])

        # Add final segment
        if current_segment['x']:
            segments.append(current_segment)

        # Add all segments to the plot
        for segment in segments:
            if len(segment['x']) > 1:  # Need at least 2 points for a line
                if custom_x_axis and segment['dates']:
                    fig.add_trace(go.Scatter(
                        x=segment['x'],
                        y=segment['y'],
                        mode='lines',
                        name='RSI',
                        line=dict(color=segment['color'], width=2),
                        hovertemplate="<b>RSI</b><br>Time: %{customdata}<br>RSI: %{y:.2f}<extra></extra>",
                        customdata=segment['dates'],
                        showlegend=False
                    ), row=2, col=1)
                else:
                    fig.add_trace(go.Scatter(
                        x=segment['x'],
                        y=segment['y'],
                        mode='lines',
                        name='RSI',
                        line=dict(color=segment['color'], width=2),
                        hovertemplate="<b>RSI</b><br>Time: %{x}<br>RSI: %{y:.2f}<extra></extra>",
                        showlegend=False
                    ), row=2, col=1)

    def run(self, host: str = DASH_HOST, port: int = DASH_PORT, debug: bool = DASH_DEBUG):
        """Run the dashboard server"""
        logger.info(f"Starting Matrix QP Dashboard on {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


if __name__ == "__main__":
    # Test the dashboard
    logging.basicConfig(level=logging.INFO)
    
    dashboard = PortfolioDashboard()
    dashboard.run()
